package com.clothingstore.view;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.AlertUtil;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Controller for Point of Sale interface
 */
public class PointOfSaleController implements Initializable {

    // Header controls
    @FXML private Label lblTransactionNumber;
    @FXML private Label lblCashier;
    @FXML private Button btnNewTransaction;

    // Product search controls
    @FXML private TextField txtProductSearch;
    @FXML private ComboBox<String> cmbProductCategory;
    @FXML private Button btnScanBarcode;
    @FXML private Button btnClearSearch;
    @FXML private TableView<Product> tblProducts;
    @FXML private TableColumn<Product, String> colProductSku;
    @FXML private TableColumn<Product, String> colProductName;
    @FXML private TableColumn<Product, String> colProductPrice;
    @FXML private TableColumn<Product, Integer> colProductStock;
    @FXML private TableColumn<Product, String> colProductAction;

    // Quick add controls
    @FXML private TextField txtQuickSku;
    @FXML private Spinner<Integer> spnQuickQuantity;
    @FXML private Button btnQuickAdd;

    // Shopping cart controls
    @FXML private Button btnClearCart;
    @FXML private TableView<TransactionItem> tblCartItems;
    @FXML private TableColumn<TransactionItem, String> colCartProduct;
    @FXML private TableColumn<TransactionItem, Integer> colCartQuantity;
    @FXML private TableColumn<TransactionItem, String> colCartUnitPrice;
    @FXML private TableColumn<TransactionItem, String> colCartDiscount;
    @FXML private TableColumn<TransactionItem, String> colCartTotal;
    @FXML private TableColumn<TransactionItem, String> colCartActions;

    // Cart summary controls
    @FXML private Label lblSubtotal;
    @FXML private Label lblDiscount;
    @FXML private Label lblTax;
    @FXML private Label lblTotal;
    @FXML private Button btnApplyDiscount;

    // Customer controls
    @FXML private TextField txtCustomerSearch;
    @FXML private Button btnNewCustomer;
    @FXML private VBox customerInfo;
    @FXML private Label lblCustomerName;
    @FXML private Label lblCustomerMembership;
    @FXML private Label lblCustomerPoints;
    @FXML private Button btnRemoveCustomer;

    // Payment controls
    @FXML private ComboBox<String> cmbPaymentMethod;
    @FXML private TextField txtAmountReceived;
    @FXML private Label lblChange;
    @FXML private TextArea txtNotes;

    // Action buttons
    @FXML private Button btnProcessPayment;
    @FXML private Button btnHoldTransaction;
    @FXML private Button btnVoidTransaction;

    // Status bar
    @FXML private Label lblStatus;
    @FXML private Label lblItemCount;
    @FXML private Label lblTransactionTime;

    // Data and services
    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ObservableList<TransactionItem> cartItems;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionService transactionService;
    private NumberFormat currencyFormat;
    private Timer clockTimer;
    
    // Current transaction data
    private Transaction currentTransaction;
    private Customer selectedCustomer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionService = TransactionService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        cartItems = FXCollections.observableArrayList();
        
        setupTables();
        setupControls();
        setupClock();
        loadProducts();
        startNewTransaction();
    }

    private void setupTables() {
        // Product table setup
        colProductSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductStock.setCellValueFactory(new PropertyValueFactory<>("stockQuantity"));
        
        colProductPrice.setCellValueFactory(cellData -> {
            BigDecimal price = cellData.getValue().getPrice();
            return new SimpleStringProperty(price != null ? currencyFormat.format(price) : "");
        });
        
        // Add button column for products
        colProductAction.setCellFactory(col -> new TableCell<Product, String>() {
            private final Button addBtn = new Button("+");
            
            {
                addBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    addProductToCart(product, 1);
                });
                addBtn.setStyle("-fx-font-size: 12px; -fx-padding: 2 8;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(addBtn);
                }
            }
        });
        
        tblProducts.setItems(filteredProducts);
        
        // Cart table setup
        colCartProduct.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDisplayInfo()));
        colCartQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        
        colCartUnitPrice.setCellValueFactory(cellData -> {
            BigDecimal price = cellData.getValue().getUnitPrice();
            return new SimpleStringProperty(price != null ? currencyFormat.format(price) : "");
        });
        
        colCartDiscount.setCellValueFactory(cellData -> {
            BigDecimal discount = cellData.getValue().getDiscountAmount();
            return new SimpleStringProperty(discount != null ? currencyFormat.format(discount) : "$0.00");
        });
        
        colCartTotal.setCellValueFactory(cellData -> {
            BigDecimal total = cellData.getValue().getLineTotal();
            return new SimpleStringProperty(total != null ? currencyFormat.format(total) : "");
        });
        
        // Cart actions column
        colCartActions.setCellFactory(col -> new TableCell<TransactionItem, String>() {
            private final Button removeBtn = new Button("×");
            private final Button editBtn = new Button("Edit");
            
            {
                removeBtn.setOnAction(e -> {
                    TransactionItem item = getTableView().getItems().get(getIndex());
                    removeItemFromCart(item);
                });
                
                editBtn.setOnAction(e -> {
                    TransactionItem item = getTableView().getItems().get(getIndex());
                    editCartItem(item);
                });
                
                removeBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6; -fx-background-color: #e74c3c; -fx-text-fill: white;");
                editBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new javafx.scene.layout.HBox(2, editBtn, removeBtn));
                }
            }
        });
        
        tblCartItems.setItems(cartItems);
    }

    private void setupControls() {
        // Setup spinner for quick quantity
        spnQuickQuantity.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 100, 1));
        
        // Setup payment methods
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
            "CASH", "CREDIT_CARD", "DEBIT_CARD", "GIFT_CARD"));
        cmbPaymentMethod.setValue("CASH");
        
        // Setup categories
        loadCategories();
        
        // Add listeners for real-time updates
        cartItems.addListener((javafx.collections.ListChangeListener<TransactionItem>) change -> {
            updateCartSummary();
            updateItemCount();
        });
        
        txtAmountReceived.textProperty().addListener((obs, oldVal, newVal) -> calculateChange());
    }

    private void setupClock() {
        clockTimer = new Timer(true);
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                javafx.application.Platform.runLater(() -> {
                    LocalDateTime now = LocalDateTime.now();
                    lblTransactionTime.setText("Time: " + now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        }, 0, 1000);
    }

    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            applyProductFilters();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void loadCategories() {
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            cmbProductCategory.setItems(categoryItems);
            cmbProductCategory.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void startNewTransaction() {
        currentTransaction = new Transaction();
        currentTransaction.setTransactionNumber("TXN" + System.currentTimeMillis());
        currentTransaction.setCashierName("Admin"); // TODO: Get from logged-in user

        cartItems.clear();
        selectedCustomer = null;
        customerInfo.setVisible(false);
        txtCustomerSearch.clear();
        txtAmountReceived.clear();
        txtNotes.clear();

        lblTransactionNumber.setText("Transaction: " + currentTransaction.getTransactionNumber());
        updateCartSummary();
        updateItemCount();
        setStatus("New transaction started");
    }

    private void applyProductFilters() {
        String searchTerm = txtProductSearch.getText();
        String selectedCategory = cmbProductCategory.getValue();

        List<Product> filtered = allProducts.stream()
            .filter(product -> matchesSearchFilter(product, searchTerm))
            .filter(product -> matchesCategoryFilter(product, selectedCategory))
            .collect(java.util.stream.Collectors.toList());

        filteredProducts.setAll(filtered);
    }

    private boolean matchesSearchFilter(Product product, String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }

        String term = searchTerm.toLowerCase();
        return (product.getName() != null && product.getName().toLowerCase().contains(term)) ||
               (product.getSku() != null && product.getSku().toLowerCase().contains(term)) ||
               (product.getDescription() != null && product.getDescription().toLowerCase().contains(term));
    }

    private boolean matchesCategoryFilter(Product product, String selectedCategory) {
        return selectedCategory == null || "All Categories".equals(selectedCategory) ||
               selectedCategory.equals(product.getCategory());
    }

    private void addProductToCart(Product product, int quantity) {
        if (product.getStockQuantity() < quantity) {
            AlertUtil.showWarning("Insufficient Stock",
                "Only " + product.getStockQuantity() + " items available for " + product.getName());
            return;
        }

        // Check if product already in cart
        Optional<TransactionItem> existingItem = cartItems.stream()
            .filter(item -> item.getProductId().equals(product.getId()))
            .findFirst();

        if (existingItem.isPresent()) {
            TransactionItem item = existingItem.get();
            int newQuantity = item.getQuantity() + quantity;

            if (product.getStockQuantity() < newQuantity) {
                AlertUtil.showWarning("Insufficient Stock",
                    "Cannot add more. Total would exceed available stock.");
                return;
            }

            item.setQuantity(newQuantity);
        } else {
            TransactionItem newItem = new TransactionItem(product, quantity);
            cartItems.add(newItem);
        }

        tblCartItems.refresh();
        setStatus("Added " + product.getName() + " to cart");
    }

    private void removeItemFromCart(TransactionItem item) {
        cartItems.remove(item);
        setStatus("Removed " + item.getProductName() + " from cart");
    }

    private void editCartItem(TransactionItem item) {
        String input = AlertUtil.showTextInput("Edit Quantity",
            "Product: " + item.getProductName(),
            "Enter new quantity:",
            String.valueOf(item.getQuantity())).orElse(null);

        if (input != null) {
            try {
                int newQuantity = Integer.parseInt(input);
                if (newQuantity <= 0) {
                    removeItemFromCart(item);
                    return;
                }

                // Check stock availability
                Optional<Product> product = allProducts.stream()
                    .filter(p -> p.getId().equals(item.getProductId()))
                    .findFirst();

                if (product.isPresent() && product.get().getStockQuantity() < newQuantity) {
                    AlertUtil.showWarning("Insufficient Stock",
                        "Only " + product.get().getStockQuantity() + " items available.");
                    return;
                }

                item.setQuantity(newQuantity);
                tblCartItems.refresh();
                setStatus("Updated quantity for " + item.getProductName());

            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Input", "Please enter a valid number.");
            }
        }
    }

    private void updateCartSummary() {
        if (cartItems.isEmpty()) {
            lblSubtotal.setText("$0.00");
            lblDiscount.setText("$0.00");
            lblTax.setText("$0.00");
            lblTotal.setText("$0.00");
            return;
        }

        // Update transaction items
        currentTransaction.setItems(new java.util.ArrayList<>(cartItems));
        currentTransaction.setCustomer(selectedCustomer);
        currentTransaction.recalculateAmounts();

        // Update labels
        lblSubtotal.setText(currencyFormat.format(currentTransaction.getSubtotal()));
        lblDiscount.setText(currencyFormat.format(currentTransaction.getDiscountAmount()));
        lblTax.setText(currencyFormat.format(currentTransaction.getTaxAmount()));
        lblTotal.setText(currencyFormat.format(currentTransaction.getTotalAmount()));

        calculateChange();
    }

    private void updateItemCount() {
        int totalItems = cartItems.stream().mapToInt(TransactionItem::getQuantity).sum();
        lblItemCount.setText("Items: " + totalItems);
    }

    private void calculateChange() {
        try {
            String amountText = txtAmountReceived.getText();
            if (amountText == null || amountText.trim().isEmpty()) {
                lblChange.setText("$0.00");
                return;
            }

            BigDecimal amountReceived = new BigDecimal(amountText);
            BigDecimal change = amountReceived.subtract(currentTransaction.getTotalAmount());

            lblChange.setText(currencyFormat.format(change));

            if (change.compareTo(BigDecimal.ZERO) < 0) {
                lblChange.setStyle("-fx-text-fill: red;");
            } else {
                lblChange.setStyle("-fx-text-fill: green;");
            }

        } catch (NumberFormatException e) {
            lblChange.setText("Invalid");
            lblChange.setStyle("-fx-text-fill: red;");
        }
    }

    private void setStatus(String message) {
        lblStatus.setText(message);
    }

    // Event Handlers
    @FXML
    private void handleNewTransaction() {
        if (!cartItems.isEmpty()) {
            if (AlertUtil.showConfirmation("New Transaction",
                "Current transaction will be lost. Continue?")) {
                startNewTransaction();
            }
        } else {
            startNewTransaction();
        }
    }

    @FXML
    private void handleProductSearch() {
        applyProductFilters();
    }

    @FXML
    private void handleCategoryFilter() {
        applyProductFilters();
    }

    @FXML
    private void handleClearSearch() {
        txtProductSearch.clear();
        cmbProductCategory.setValue("All Categories");
        applyProductFilters();
    }

    @FXML
    private void handleScanBarcode() {
        AlertUtil.showInfo("Feature Coming Soon", "Barcode scanning will be implemented in a future version.");
    }

    @FXML
    private void handleQuickAdd() {
        String sku = txtQuickSku.getText();
        if (sku == null || sku.trim().isEmpty()) {
            AlertUtil.showWarning("Invalid Input", "Please enter a product SKU.");
            return;
        }

        try {
            Optional<Product> product = productDAO.findBySku(sku.trim());
            if (product.isPresent()) {
                int quantity = spnQuickQuantity.getValue();
                addProductToCart(product.get(), quantity);
                txtQuickSku.clear();
                spnQuickQuantity.getValueFactory().setValue(1);
            } else {
                AlertUtil.showWarning("Product Not Found", "No product found with SKU: " + sku);
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to find product: " + e.getMessage());
        }
    }

    @FXML
    private void handleClearCart() {
        if (!cartItems.isEmpty()) {
            if (AlertUtil.showConfirmation("Clear Cart", "Remove all items from cart?")) {
                cartItems.clear();
                setStatus("Cart cleared");
            }
        }
    }

    @FXML
    private void handleApplyDiscount() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before applying discount.");
            return;
        }

        String input = AlertUtil.showTextInput("Apply Discount",
            "Current subtotal: " + currencyFormat.format(currentTransaction.getSubtotal()),
            "Enter discount percentage (0-100):", "0").orElse(null);

        if (input != null) {
            try {
                double percentage = Double.parseDouble(input);
                if (percentage < 0 || percentage > 100) {
                    AlertUtil.showError("Invalid Input", "Discount percentage must be between 0 and 100.");
                    return;
                }

                BigDecimal discountAmount = currentTransaction.getSubtotal()
                    .multiply(BigDecimal.valueOf(percentage / 100));
                currentTransaction.setDiscountAmount(discountAmount);
                updateCartSummary();
                setStatus("Applied " + percentage + "% discount");

            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Input", "Please enter a valid number.");
            }
        }
    }

    @FXML
    private void handleCustomerSearch() {
        String searchTerm = txtCustomerSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            selectedCustomer = null;
            customerInfo.setVisible(false);
            updateCartSummary();
            return;
        }

        try {
            List<Customer> customers = customerDAO.searchCustomers(searchTerm.trim());
            if (customers.isEmpty()) {
                selectedCustomer = null;
                customerInfo.setVisible(false);
                setStatus("No customers found");
            } else if (customers.size() == 1) {
                selectCustomer(customers.get(0));
            } else {
                // TODO: Show customer selection dialog
                selectCustomer(customers.get(0)); // For now, select first match
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to search customers: " + e.getMessage());
        }
    }

    @FXML
    private void handleNewCustomer() {
        AlertUtil.showInfo("Feature Coming Soon", "Customer registration will be implemented next.");
    }

    @FXML
    private void handleRemoveCustomer() {
        selectedCustomer = null;
        customerInfo.setVisible(false);
        txtCustomerSearch.clear();
        updateCartSummary();
        setStatus("Customer removed from transaction");
    }

    @FXML
    private void handleAmountReceived() {
        calculateChange();
    }

    @FXML
    private void handleProcessPayment() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing payment.");
            return;
        }

        String paymentMethod = cmbPaymentMethod.getValue();
        if (paymentMethod == null) {
            AlertUtil.showWarning("Payment Method Required", "Please select a payment method.");
            return;
        }

        // Validate payment amount for cash transactions
        if ("CASH".equals(paymentMethod)) {
            try {
                String amountText = txtAmountReceived.getText();
                if (amountText == null || amountText.trim().isEmpty()) {
                    AlertUtil.showWarning("Amount Required", "Please enter the amount received.");
                    return;
                }

                BigDecimal amountReceived = new BigDecimal(amountText);
                if (amountReceived.compareTo(currentTransaction.getTotalAmount()) < 0) {
                    AlertUtil.showWarning("Insufficient Payment", "Amount received is less than total.");
                    return;
                }
            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Amount", "Please enter a valid amount.");
                return;
            }
        }

        // Process the transaction
        try {
            currentTransaction.setPaymentMethod(paymentMethod);
            currentTransaction.setNotes(txtNotes.getText());

            Transaction completedTransaction = transactionService.processTransaction(currentTransaction);

            AlertUtil.showSuccess("Payment Processed",
                "Transaction " + completedTransaction.getTransactionNumber() + " completed successfully!");

            // TODO: Print receipt

            startNewTransaction();

        } catch (TransactionService.InsufficientStockException e) {
            AlertUtil.showError("Insufficient Stock", e.getMessage());
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to process transaction: " + e.getMessage());
        }
    }

    @FXML
    private void handleHoldTransaction() {
        AlertUtil.showInfo("Feature Coming Soon", "Hold transaction feature will be implemented next.");
    }

    @FXML
    private void handleVoidTransaction() {
        if (AlertUtil.showConfirmation("Void Transaction", "Are you sure you want to void this transaction?")) {
            startNewTransaction();
        }
    }

    private void selectCustomer(Customer customer) {
        selectedCustomer = customer;
        lblCustomerName.setText(customer.getFullName());
        lblCustomerMembership.setText("Membership: " + customer.getMembershipLevel());
        lblCustomerPoints.setText("Loyalty Points: " + customer.getLoyaltyPoints());
        customerInfo.setVisible(true);
        updateCartSummary();
        setStatus("Customer selected: " + customer.getFullName());
    }

    public void shutdown() {
        if (clockTimer != null) {
            clockTimer.cancel();
        }
    }
}

package com.clothingstore.demo;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;
import java.text.NumberFormat;
import java.util.List;

/**
 * Simple Point of Sale Demo - Shows the system in action
 */
public class SimplePOSDemo {
    
    public static void main(String[] args) {
        System.out.println("CLOTHING STORE MANAGEMENT SYSTEM - LIVE DEMO");
        System.out.println("=============================================");
        
        try {
            // Initialize system
            System.out.println("\n[1] Initializing database...");
            DatabaseManager.getInstance().initializeDatabase();
            System.out.println("SUCCESS: Database ready with sample data");
            
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            TransactionService transactionService = TransactionService.getInstance();
            NumberFormat currency = NumberFormat.getCurrencyInstance();
            
            // Show available products
            System.out.println("\n[2] Loading product catalog...");
            List<Product> products = productDAO.findAll();
            System.out.println("PRODUCT CATALOG (" + products.size() + " items):");
            System.out.println("SKU      | Product Name           | Price    | Stock");
            System.out.println("---------|------------------------|----------|------");
            for (int i = 0; i < Math.min(5, products.size()); i++) {
                Product p = products.get(i);
                System.out.printf("%-8s | %-22s | %-8s | %d\n", 
                    p.getSku(), 
                    p.getName().length() > 22 ? p.getName().substring(0, 19) + "..." : p.getName(),
                    currency.format(p.getPrice()), 
                    p.getStockQuantity());
            }
            
            // Show customers
            System.out.println("\n[3] Loading customer database...");
            List<Customer> customers = customerDAO.findAll();
            System.out.println("CUSTOMER DATABASE (" + customers.size() + " customers):");
            for (Customer c : customers) {
                System.out.printf("- %s (%s) - %s member, %d points, spent %s\n", 
                    c.getFullName(), c.getEmail(), c.getMembershipLevel(), 
                    c.getLoyaltyPoints(), currency.format(c.getTotalSpent()));
            }
            
            // Simulate a transaction
            System.out.println("\n[4] Processing a sample transaction...");
            
            // Get customer and products
            Customer customer = customers.get(0); // John Doe
            Product tshirt = products.stream()
                .filter(p -> p.getName().contains("T-Shirt"))
                .findFirst().get();
            Product jeans = products.stream()
                .filter(p -> p.getName().contains("Jeans"))
                .findFirst().get();
            
            System.out.println("Customer: " + customer.getFullName() + " (Current points: " + customer.getLoyaltyPoints() + ")");
            System.out.println("Items to purchase:");
            System.out.println("- 2x " + tshirt.getName() + " @ " + currency.format(tshirt.getPrice()) + " each");
            System.out.println("- 1x " + jeans.getName() + " @ " + currency.format(jeans.getPrice()));
            
            // Record initial stock
            int initialTshirtStock = tshirt.getStockQuantity();
            int initialJeansStock = jeans.getStockQuantity();
            double initialCustomerSpent = customer.getTotalSpent();
            int initialCustomerPoints = customer.getLoyaltyPoints();
            
            // Create transaction
            Transaction transaction = new Transaction();
            transaction.setCustomer(customer);
            transaction.setCashierName("Demo Cashier");
            transaction.setPaymentMethod("CREDIT_CARD");
            
            // Add items
            transaction.addItem(new TransactionItem(tshirt, 2));
            transaction.addItem(new TransactionItem(jeans, 1));
            
            // Show calculation
            transaction.recalculateAmounts();
            System.out.println("\nTRANSACTION CALCULATION:");
            System.out.println("Subtotal: " + currency.format(transaction.getSubtotal()));
            System.out.println("Customer discount (5% for Silver): " + currency.format(transaction.getDiscountAmount()));
            System.out.println("TOTAL: " + currency.format(transaction.getTotalAmount()));
            
            // Process transaction
            System.out.println("\n[5] Processing payment...");
            Transaction completed = transactionService.processTransaction(transaction);
            System.out.println("SUCCESS: Transaction " + completed.getTransactionNumber() + " completed!");
            
            // Show updates
            System.out.println("\n[6] Verifying system updates...");
            
            // Check stock updates
            Product updatedTshirt = productDAO.findById(tshirt.getId()).get();
            Product updatedJeans = productDAO.findById(jeans.getId()).get();
            System.out.println("INVENTORY UPDATES:");
            System.out.println("- T-Shirt stock: " + initialTshirtStock + " -> " + updatedTshirt.getStockQuantity());
            System.out.println("- Jeans stock: " + initialJeansStock + " -> " + updatedJeans.getStockQuantity());
            
            // Check customer updates
            Customer updatedCustomer = customerDAO.findById(customer.getId()).get();
            System.out.println("CUSTOMER UPDATES:");
            System.out.println("- Total spent: " + currency.format(initialCustomerSpent) + " -> " + currency.format(updatedCustomer.getTotalSpent()));
            System.out.println("- Loyalty points: " + initialCustomerPoints + " -> " + updatedCustomer.getLoyaltyPoints());
            System.out.println("- Membership level: " + updatedCustomer.getMembershipLevel());
            
            // Show business intelligence
            System.out.println("\n[7] Business intelligence summary...");
            
            List<Product> lowStock = productDAO.findLowStockProducts();
            System.out.println("Low stock alerts: " + lowStock.size() + " products");
            
            List<Customer> topCustomers = customerDAO.findTopCustomers(3);
            System.out.println("TOP CUSTOMERS:");
            for (int i = 0; i < topCustomers.size(); i++) {
                Customer c = topCustomers.get(i);
                System.out.printf("%d. %s - %s (%s)\n", 
                    i+1, c.getFullName(), currency.format(c.getTotalSpent()), c.getMembershipLevel());
            }
            
            // Show categories
            List<String> categories = productDAO.getAllCategories();
            System.out.println("Product categories: " + String.join(", ", categories));
            
            System.out.println("\n========================================");
            System.out.println("DEMO COMPLETED SUCCESSFULLY!");
            System.out.println("========================================");
            System.out.println("Features demonstrated:");
            System.out.println("+ Product catalog management");
            System.out.println("+ Customer database with loyalty program");
            System.out.println("+ Complete transaction processing");
            System.out.println("+ Automatic inventory updates");
            System.out.println("+ Customer loyalty point calculation");
            System.out.println("+ Membership level management");
            System.out.println("+ Discount calculations (tax-free pricing)");
            System.out.println("+ Business intelligence reporting");
            System.out.println("+ Real-time stock tracking");
            System.out.println("+ Multi-item transaction support");
            
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
        }
    }
}

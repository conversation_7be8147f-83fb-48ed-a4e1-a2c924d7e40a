package com.clothingstore.demo;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Scanner;

/**
 * Interactive Point of Sale Demo
 */
public class InteractivePOSDemo {
    
    private static ProductDAO productDAO;
    private static CustomerDAO customerDAO;
    private static TransactionService transactionService;
    private static NumberFormat currencyFormat;
    private static Scanner scanner;
    
    public static void main(String[] args) {
        try {
            // Initialize system
            System.out.println("CLOTHING STORE MANAGEMENT SYSTEM - INTERACTIVE DEMO");
            System.out.println("=====================================================");
            
            DatabaseManager.getInstance().initializeDatabase();
            productDAO = ProductDAO.getInstance();
            customerDAO = CustomerDAO.getInstance();
            transactionService = TransactionService.getInstance();
            currencyFormat = NumberFormat.getCurrencyInstance();
            scanner = new Scanner(System.in);
            
            // Show main menu
            showMainMenu();
            
        } catch (Exception e) {
            System.err.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
            if (scanner != null) scanner.close();
        }
    }
    
    private static void showMainMenu() throws SQLException {
        while (true) {
            System.out.println("\n📋 MAIN MENU");
            System.out.println("=============");
            System.out.println("1. 🛒 Point of Sale (Process Transaction)");
            System.out.println("2. 👕 View Products");
            System.out.println("3. 👥 View Customers");
            System.out.println("4. 📊 View Reports");
            System.out.println("5. 🔧 Inventory Management");
            System.out.println("0. ❌ Exit");
            System.out.print("\nSelect option: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    runPointOfSale();
                    break;
                case "2":
                    viewProducts();
                    break;
                case "3":
                    viewCustomers();
                    break;
                case "4":
                    viewReports();
                    break;
                case "5":
                    inventoryManagement();
                    break;
                case "0":
                    System.out.println("👋 Thank you for using Clothing Store Management System!");
                    return;
                default:
                    System.out.println("❌ Invalid option. Please try again.");
            }
        }
    }
    
    private static void runPointOfSale() throws SQLException {
        System.out.println("\n🛒 POINT OF SALE");
        System.out.println("=================");
        
        Transaction transaction = new Transaction();
        transaction.setTransactionNumber("TXN" + System.currentTimeMillis());
        transaction.setCashierName("Demo User");
        
        // Select customer
        Customer customer = selectCustomer();
        if (customer != null) {
            transaction.setCustomer(customer);
            System.out.println("✅ Customer: " + customer.getFullName() + " (" + customer.getMembershipLevel() + ")");
        }
        
        // Add products to cart
        while (true) {
            System.out.println("\n📦 ADD PRODUCTS TO CART");
            System.out.println("Available products:");
            
            List<Product> products = productDAO.findAll();
            for (int i = 0; i < Math.min(5, products.size()); i++) {
                Product p = products.get(i);
                System.out.printf("%d. %s (%s) - %s - Stock: %d\n", 
                    i+1, p.getName(), p.getSku(), currencyFormat.format(p.getPrice()), p.getStockQuantity());
            }
            
            System.out.print("\nSelect product (1-" + Math.min(5, products.size()) + ") or 'done' to checkout: ");
            String input = scanner.nextLine().trim();
            
            if ("done".equalsIgnoreCase(input)) {
                break;
            }
            
            try {
                int productIndex = Integer.parseInt(input) - 1;
                if (productIndex >= 0 && productIndex < Math.min(5, products.size())) {
                    Product selectedProduct = products.get(productIndex);
                    
                    System.out.print("Enter quantity: ");
                    int quantity = Integer.parseInt(scanner.nextLine().trim());
                    
                    if (quantity > 0 && quantity <= selectedProduct.getStockQuantity()) {
                        TransactionItem item = new TransactionItem(selectedProduct, quantity);
                        transaction.addItem(item);
                        System.out.println("✅ Added " + quantity + "x " + selectedProduct.getName() + " to cart");
                    } else {
                        System.out.println("❌ Invalid quantity or insufficient stock");
                    }
                } else {
                    System.out.println("❌ Invalid product selection");
                }
            } catch (NumberFormatException e) {
                System.out.println("❌ Invalid input");
            }
        }
        
        if (transaction.getItems().isEmpty()) {
            System.out.println("❌ No items in cart. Transaction cancelled.");
            return;
        }
        
        // Show transaction summary
        transaction.recalculateAmounts();
        System.out.println("\n🧾 TRANSACTION SUMMARY");
        System.out.println("======================");
        System.out.println("Transaction: " + transaction.getTransactionNumber());
        if (customer != null) {
            System.out.println("Customer: " + customer.getFullName());
        }
        System.out.println("\nItems:");
        for (TransactionItem item : transaction.getItems()) {
            System.out.printf("  %dx %s @ %s = %s\n", 
                item.getQuantity(), item.getProductName(), 
                currencyFormat.format(item.getUnitPrice()),
                currencyFormat.format(item.getLineTotal()));
        }
        System.out.println("\nSubtotal: " + currencyFormat.format(transaction.getSubtotal()));
        if (transaction.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            System.out.println("Discount: -" + currencyFormat.format(transaction.getDiscountAmount()));
        }
        System.out.println("Tax: " + currencyFormat.format(transaction.getTaxAmount()));
        System.out.println("TOTAL: " + currencyFormat.format(transaction.getTotalAmount()));
        
        // Process payment
        System.out.print("\nProcess payment? (y/n): ");
        if ("y".equalsIgnoreCase(scanner.nextLine().trim())) {
            try {
                transaction.setPaymentMethod("CASH");
                Transaction completedTransaction = transactionService.processTransaction(transaction);
                
                System.out.println("\n✅ PAYMENT SUCCESSFUL!");
                System.out.println("Transaction ID: " + completedTransaction.getId());
                System.out.println("Receipt printed! 🧾");
                
                if (customer != null) {
                    Customer updatedCustomer = customerDAO.findById(customer.getId()).get();
                    System.out.println("Customer earned " + 
                        (updatedCustomer.getLoyaltyPoints() - customer.getLoyaltyPoints()) + " loyalty points!");
                }
                
            } catch (Exception e) {
                System.out.println("❌ Payment failed: " + e.getMessage());
            }
        } else {
            System.out.println("❌ Transaction cancelled");
        }
    }
    
    private static Customer selectCustomer() throws SQLException {
        System.out.print("Enter customer phone or email (or press Enter to skip): ");
        String search = scanner.nextLine().trim();
        
        if (search.isEmpty()) {
            return null;
        }
        
        List<Customer> customers = customerDAO.searchCustomers(search);
        if (customers.isEmpty()) {
            System.out.println("❌ No customer found");
            return null;
        } else if (customers.size() == 1) {
            return customers.get(0);
        } else {
            System.out.println("Multiple customers found:");
            for (int i = 0; i < customers.size(); i++) {
                Customer c = customers.get(i);
                System.out.printf("%d. %s (%s) - %s\n", i+1, c.getFullName(), c.getEmail(), c.getMembershipLevel());
            }
            System.out.print("Select customer (1-" + customers.size() + "): ");
            try {
                int index = Integer.parseInt(scanner.nextLine().trim()) - 1;
                if (index >= 0 && index < customers.size()) {
                    return customers.get(index);
                }
            } catch (NumberFormatException e) {
                // Invalid input
            }
            System.out.println("❌ Invalid selection");
            return null;
        }
    }
    
    private static void viewProducts() throws SQLException {
        System.out.println("\n👕 PRODUCT CATALOG");
        System.out.println("==================");
        
        List<Product> products = productDAO.findAll();
        System.out.printf("%-10s %-25s %-15s %-10s %-8s %-8s\n", 
            "SKU", "Name", "Category", "Price", "Stock", "Status");
        System.out.println("-".repeat(80));
        
        for (Product p : products) {
            String status = p.isLowStock() ? "LOW STOCK" : "OK";
            System.out.printf("%-10s %-25s %-15s %-10s %-8d %-8s\n", 
                p.getSku(), 
                p.getName().length() > 25 ? p.getName().substring(0, 22) + "..." : p.getName(),
                p.getCategory(), 
                currencyFormat.format(p.getPrice()), 
                p.getStockQuantity(), 
                status);
        }
        
        System.out.println("\nTotal products: " + products.size());
        
        List<Product> lowStock = productDAO.findLowStockProducts();
        if (!lowStock.isEmpty()) {
            System.out.println("⚠️  Low stock items: " + lowStock.size());
        }
    }
    
    private static void viewCustomers() throws SQLException {
        System.out.println("\n👥 CUSTOMER DATABASE");
        System.out.println("====================");
        
        List<Customer> customers = customerDAO.findAll();
        System.out.printf("%-20s %-25s %-12s %-8s %-12s\n", 
            "Name", "Email", "Membership", "Points", "Total Spent");
        System.out.println("-".repeat(80));
        
        for (Customer c : customers) {
            System.out.printf("%-20s %-25s %-12s %-8d %-12s\n", 
                c.getFullName(),
                c.getEmail() != null ? c.getEmail() : "N/A",
                c.getMembershipLevel(), 
                c.getLoyaltyPoints(), 
                currencyFormat.format(c.getTotalSpent()));
        }
        
        System.out.println("\nTotal customers: " + customers.size());
    }
    
    private static void viewReports() throws SQLException {
        System.out.println("\n📊 BUSINESS REPORTS");
        System.out.println("===================");
        
        // Product summary
        List<Product> products = productDAO.findAll();
        BigDecimal totalInventoryValue = products.stream()
            .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        System.out.println("📦 INVENTORY SUMMARY:");
        System.out.println("  Total products: " + products.size());
        System.out.println("  Total inventory value: " + currencyFormat.format(totalInventoryValue));
        System.out.println("  Low stock items: " + productDAO.findLowStockProducts().size());
        
        // Customer summary
        List<Customer> customers = customerDAO.findAll();
        System.out.println("\n👥 CUSTOMER SUMMARY:");
        System.out.println("  Total customers: " + customers.size());
        System.out.println("  Bronze members: " + customerDAO.findByMembershipLevel("BRONZE").size());
        System.out.println("  Silver members: " + customerDAO.findByMembershipLevel("SILVER").size());
        System.out.println("  Gold members: " + customerDAO.findByMembershipLevel("GOLD").size());
        System.out.println("  Platinum members: " + customerDAO.findByMembershipLevel("PLATINUM").size());
        
        // Top customers
        List<Customer> topCustomers = customerDAO.findTopCustomers(3);
        System.out.println("\n🏆 TOP CUSTOMERS:");
        for (int i = 0; i < topCustomers.size(); i++) {
            Customer c = topCustomers.get(i);
            System.out.printf("  %d. %s - %s (%s, %d points)\n", 
                i+1, c.getFullName(), currencyFormat.format(c.getTotalSpent()), 
                c.getMembershipLevel(), c.getLoyaltyPoints());
        }
    }
    
    private static void inventoryManagement() throws SQLException {
        System.out.println("\n🔧 INVENTORY MANAGEMENT");
        System.out.println("=======================");
        
        List<Product> lowStock = productDAO.findLowStockProducts();
        if (lowStock.isEmpty()) {
            System.out.println("✅ All products have adequate stock levels");
        } else {
            System.out.println("⚠️  Low stock products:");
            for (Product p : lowStock) {
                System.out.printf("  %s (%s) - Stock: %d, Min: %d\n", 
                    p.getName(), p.getSku(), p.getStockQuantity(), p.getMinStockLevel());
            }
        }
        
        System.out.print("\nAdjust stock for a product? Enter SKU (or press Enter to skip): ");
        String sku = scanner.nextLine().trim();
        
        if (!sku.isEmpty()) {
            try {
                var product = productDAO.findBySku(sku);
                if (product.isPresent()) {
                    Product p = product.get();
                    System.out.println("Current stock for " + p.getName() + ": " + p.getStockQuantity());
                    System.out.print("Enter new stock level: ");
                    int newStock = Integer.parseInt(scanner.nextLine().trim());
                    
                    if (newStock >= 0) {
                        productDAO.updateStock(p.getId(), newStock);
                        System.out.println("✅ Stock updated successfully!");
                    } else {
                        System.out.println("❌ Invalid stock level");
                    }
                } else {
                    System.out.println("❌ Product not found");
                }
            } catch (NumberFormatException e) {
                System.out.println("❌ Invalid input");
            }
        }
    }
}

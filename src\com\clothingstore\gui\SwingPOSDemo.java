package com.clothingstore.gui;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;
import java.awt.*;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

/**
 * Simple Swing-based POS GUI Demo
 */
public class SwingPOSDemo extends JFrame {
    
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionService transactionService;
    private NumberFormat currencyFormat;
    
    private Transaction currentTransaction;
    private Customer selectedCustomer;
    
    // GUI Components
    private JTable productTable;
    private JTable cartTable;
    private DefaultTableModel productTableModel;
    private DefaultTableModel cartTableModel;
    private JLabel lblSubtotal;
    private JLabel lblDiscount;
    private JLabel lblTotal;
    private JLabel lblCustomerInfo;
    private JTextField txtCustomerSearch;
    private JTextField txtDiscountAmount;
    private JButton btnApplyDiscount;
    private JButton btnClearDiscount;
    private JButton btnProcessPayment;
    
    public SwingPOSDemo() {
        super("Clothing Store POS System - Manual Discount Demo");
        initializeServices();
        initializeGUI();
        loadData();
        startNewTransaction();
    }
    
    private void initializeServices() {
        try {
            DatabaseManager.getInstance().initializeDatabase();
            productDAO = ProductDAO.getInstance();
            customerDAO = CustomerDAO.getInstance();
            transactionService = TransactionService.getInstance();
            currencyFormat = NumberFormat.getCurrencyInstance();
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Database initialization failed: " + e.getMessage(), 
                "Error", JOptionPane.ERROR_MESSAGE);
            System.exit(1);
        }
    }
    
    private void initializeGUI() {
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // Create main panels
        JPanel topPanel = createTopPanel();
        JPanel centerPanel = createCenterPanel();
        JPanel bottomPanel = createBottomPanel();
        
        add(topPanel, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
        
        setSize(1200, 800);
        setLocationRelativeTo(null);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createTitledBorder("Customer Information"));
        
        panel.add(new JLabel("Search Customer:"));
        txtCustomerSearch = new JTextField(20);
        panel.add(txtCustomerSearch);
        
        JButton btnSearchCustomer = new JButton("Search");
        btnSearchCustomer.addActionListener(e -> searchCustomer());
        panel.add(btnSearchCustomer);
        
        JButton btnClearCustomer = new JButton("Clear");
        btnClearCustomer.addActionListener(e -> clearCustomer());
        panel.add(btnClearCustomer);
        
        lblCustomerInfo = new JLabel("No customer selected");
        lblCustomerInfo.setFont(lblCustomerInfo.getFont().deriveFont(Font.BOLD));
        panel.add(lblCustomerInfo);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new GridLayout(1, 2, 10, 0));
        
        // Product panel
        JPanel productPanel = new JPanel(new BorderLayout());
        productPanel.setBorder(BorderFactory.createTitledBorder("Products"));
        
        String[] productColumns = {"SKU", "Name", "Price", "Stock"};
        productTableModel = new DefaultTableModel(productColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) { return false; }
        };
        productTable = new JTable(productTableModel);
        productTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JButton btnAddToCart = new JButton("Add to Cart");
        btnAddToCart.addActionListener(e -> addToCart());
        
        productPanel.add(new JScrollPane(productTable), BorderLayout.CENTER);
        productPanel.add(btnAddToCart, BorderLayout.SOUTH);
        
        // Cart panel
        JPanel cartPanel = new JPanel(new BorderLayout());
        cartPanel.setBorder(BorderFactory.createTitledBorder("Shopping Cart"));
        
        String[] cartColumns = {"Product", "Qty", "Price", "Total"};
        cartTableModel = new DefaultTableModel(cartColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) { return false; }
        };
        cartTable = new JTable(cartTableModel);
        
        JButton btnRemoveFromCart = new JButton("Remove Item");
        btnRemoveFromCart.addActionListener(e -> removeFromCart());
        
        cartPanel.add(new JScrollPane(cartTable), BorderLayout.CENTER);
        cartPanel.add(btnRemoveFromCart, BorderLayout.SOUTH);
        
        panel.add(productPanel);
        panel.add(cartPanel);
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 1));
        
        // Discount panel
        JPanel discountPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        discountPanel.setBorder(BorderFactory.createTitledBorder("Manual Discount"));
        
        discountPanel.add(new JLabel("Discount Amount ($):"));
        txtDiscountAmount = new JTextField(10);
        discountPanel.add(txtDiscountAmount);
        
        btnApplyDiscount = new JButton("Apply Discount");
        btnApplyDiscount.addActionListener(e -> applyDiscount());
        discountPanel.add(btnApplyDiscount);
        
        btnClearDiscount = new JButton("Clear Discount");
        btnClearDiscount.addActionListener(e -> clearDiscount());
        discountPanel.add(btnClearDiscount);
        
        // Totals panel
        JPanel totalsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        totalsPanel.setBorder(BorderFactory.createTitledBorder("Transaction Totals"));
        
        lblSubtotal = new JLabel("Subtotal: $0.00");
        lblDiscount = new JLabel("Discount: $0.00");
        lblTotal = new JLabel("TOTAL: $0.00");
        lblTotal.setFont(lblTotal.getFont().deriveFont(Font.BOLD, 16f));
        
        totalsPanel.add(lblSubtotal);
        totalsPanel.add(new JLabel(" | "));
        totalsPanel.add(lblDiscount);
        totalsPanel.add(new JLabel(" | "));
        totalsPanel.add(lblTotal);
        
        btnProcessPayment = new JButton("Process Payment");
        btnProcessPayment.addActionListener(e -> processPayment());
        btnProcessPayment.setFont(btnProcessPayment.getFont().deriveFont(Font.BOLD, 14f));
        totalsPanel.add(btnProcessPayment);
        
        panel.add(discountPanel);
        panel.add(totalsPanel);
        
        return panel;
    }
    
    private void loadData() {
        try {
            List<Product> products = productDAO.findAll();
            productTableModel.setRowCount(0);
            
            for (Product product : products) {
                Object[] row = {
                    product.getSku(),
                    product.getName(),
                    currencyFormat.format(product.getPrice()),
                    product.getStockQuantity()
                };
                productTableModel.addRow(row);
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Failed to load products: " + e.getMessage(), 
                "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void startNewTransaction() {
        currentTransaction = new Transaction();
        currentTransaction.setCashierName("Demo Cashier");
        cartTableModel.setRowCount(0);
        updateTotals();
    }
    
    private void searchCustomer() {
        String searchTerm = txtCustomerSearch.getText().trim();
        if (searchTerm.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please enter a search term", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        try {
            List<Customer> customers = customerDAO.searchCustomers(searchTerm);
            if (customers.isEmpty()) {
                JOptionPane.showMessageDialog(this, "No customers found", "Info", JOptionPane.INFORMATION_MESSAGE);
            } else if (customers.size() == 1) {
                selectCustomer(customers.get(0));
            } else {
                // Show selection dialog
                Customer[] customerArray = customers.toArray(new Customer[0]);
                Customer selected = (Customer) JOptionPane.showInputDialog(this, 
                    "Multiple customers found. Select one:", "Customer Selection",
                    JOptionPane.QUESTION_MESSAGE, null, customerArray, customerArray[0]);
                if (selected != null) {
                    selectCustomer(selected);
                }
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Search failed: " + e.getMessage(), 
                "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void selectCustomer(Customer customer) {
        selectedCustomer = customer;
        currentTransaction.setCustomer(customer);
        lblCustomerInfo.setText(String.format("%s (%s) - Suggested Discount: %.0f%%", 
            customer.getFullName(), customer.getMembershipLevel(), 
            customer.getSuggestedDiscountPercentage() * 100));
        updateTotals();
    }
    
    private void clearCustomer() {
        selectedCustomer = null;
        currentTransaction.setCustomer(null);
        txtCustomerSearch.setText("");
        lblCustomerInfo.setText("No customer selected");
        updateTotals();
    }
    
    private void addToCart() {
        int selectedRow = productTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a product", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        String quantityStr = JOptionPane.showInputDialog(this, "Enter quantity:", "1");
        if (quantityStr == null) return;
        
        try {
            int quantity = Integer.parseInt(quantityStr);
            if (quantity <= 0) {
                JOptionPane.showMessageDialog(this, "Quantity must be positive", "Error", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            String sku = (String) productTableModel.getValueAt(selectedRow, 0);
            Product product = productDAO.findBySku(sku).orElse(null);
            
            if (product == null) {
                JOptionPane.showMessageDialog(this, "Product not found", "Error", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            if (quantity > product.getStockQuantity()) {
                JOptionPane.showMessageDialog(this, "Insufficient stock", "Error", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            TransactionItem item = new TransactionItem(product, quantity);
            currentTransaction.addItem(item);
            
            Object[] row = {
                product.getName(),
                quantity,
                currencyFormat.format(product.getPrice()),
                currencyFormat.format(item.getLineTotal())
            };
            cartTableModel.addRow(row);
            
            updateTotals();
            
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Invalid quantity", "Error", JOptionPane.ERROR_MESSAGE);
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Database error: " + e.getMessage(), 
                "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void removeFromCart() {
        int selectedRow = cartTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select an item to remove", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        currentTransaction.getItems().remove(selectedRow);
        cartTableModel.removeRow(selectedRow);
        updateTotals();
    }
    
    private void applyDiscount() {
        String discountStr = txtDiscountAmount.getText().trim();
        if (discountStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please enter a discount amount", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        try {
            if (discountStr.endsWith("%")) {
                double percentage = Double.parseDouble(discountStr.substring(0, discountStr.length() - 1));
                if (percentage < 0 || percentage > 100) {
                    JOptionPane.showMessageDialog(this, "Percentage must be between 0 and 100", 
                        "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                currentTransaction.applyManualDiscountPercentage(percentage / 100.0);
            } else {
                double amount = Double.parseDouble(discountStr);
                if (amount < 0) {
                    JOptionPane.showMessageDialog(this, "Discount amount cannot be negative", 
                        "Error", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                currentTransaction.applyManualDiscount(new BigDecimal(amount));
            }
            updateTotals();
            JOptionPane.showMessageDialog(this, "Discount applied successfully", "Success", JOptionPane.INFORMATION_MESSAGE);
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Invalid discount amount", "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void clearDiscount() {
        currentTransaction.clearDiscount();
        txtDiscountAmount.setText("");
        updateTotals();
        JOptionPane.showMessageDialog(this, "Discount cleared", "Info", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void updateTotals() {
        currentTransaction.recalculateAmounts();
        lblSubtotal.setText("Subtotal: " + currencyFormat.format(currentTransaction.getSubtotal()));
        lblDiscount.setText("Discount: " + currencyFormat.format(currentTransaction.getDiscountAmount()));
        lblTotal.setText("TOTAL: " + currencyFormat.format(currentTransaction.getTotalAmount()));
    }
    
    private void processPayment() {
        if (currentTransaction.getItems().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Cart is empty", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        try {
            currentTransaction.setPaymentMethod("CASH");
            Transaction completed = transactionService.processTransaction(currentTransaction);
            
            JOptionPane.showMessageDialog(this, 
                "Payment processed successfully!\nTransaction: " + completed.getTransactionNumber(), 
                "Success", JOptionPane.INFORMATION_MESSAGE);
            
            startNewTransaction();
            loadData(); // Refresh product stock
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Payment failed: " + e.getMessage(), 
                "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new SwingPOSDemo().setVisible(true);
        });
    }
}

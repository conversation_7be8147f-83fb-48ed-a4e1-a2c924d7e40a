@echo off
title Install JavaFX Runtime - Clothing Store POS System
echo ========================================
echo  INSTALLING JAVAFX RUNTIME COMPONENTS
echo ========================================
echo.

cd /d "%~dp0"

echo Installing JavaFX runtime components for the Clothing Store POS System...
echo.

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher first
    pause
    exit /b 1
)

echo [1/4] Checking current JavaFX installation...
if exist "javafx-sdk-11.0.2\lib" (
    echo ✅ JavaFX SDK 11.0.2 found
) else (
    echo ⚠️  JavaFX SDK 11.0.2 not found, will download...
)

echo.
echo [2/4] Downloading JavaFX 17 SDK (latest stable)...
if not exist "javafx-17-sdk.zip" (
    echo Downloading JavaFX 17 SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_windows-x64_bin-sdk.zip' -OutFile 'javafx-17-sdk.zip'"
    if errorlevel 1 (
        echo ERROR: Failed to download JavaFX 17 SDK
        echo Continuing with existing JavaFX 11...
        goto compile
    )
    echo ✅ JavaFX 17 SDK downloaded successfully
) else (
    echo ✅ JavaFX 17 SDK already downloaded
)

echo.
echo [3/4] Extracting JavaFX 17 SDK...
if not exist "javafx-sdk-17.0.2" (
    echo Extracting JavaFX 17 SDK...
    powershell -Command "Expand-Archive -Path 'javafx-17-sdk.zip' -DestinationPath '.' -Force"
    if errorlevel 1 (
        echo ERROR: Failed to extract JavaFX 17 SDK
        echo Continuing with existing JavaFX 11...
        goto compile
    )
    echo ✅ JavaFX 17 SDK extracted successfully
) else (
    echo ✅ JavaFX 17 SDK already extracted
)

:compile
echo.
echo [4/4] Compiling JavaFX applications with runtime support...

REM Try JavaFX 17 first
if exist "javafx-sdk-17.0.2\lib" (
    echo Compiling with JavaFX 17...
    javac -cp "bin;lib/sqlite-jdbc-********.jar" --module-path "javafx-sdk-17.0.2/lib" --add-modules javafx.controls,javafx.fxml -d bin src/com/clothingstore/BasicJavaFXApp.java
    if not errorlevel 1 (
        echo ✅ JavaFX 17 compilation successful
        set JAVAFX_VERSION=17
        goto create_launcher
    )
)

REM Fallback to JavaFX 11
if exist "javafx-sdk-11.0.2\lib" (
    echo Compiling with JavaFX 11...
    javac -cp "bin;lib/sqlite-jdbc-********.jar" --module-path "javafx-sdk-11.0.2/lib" --add-modules javafx.controls,javafx.fxml -d bin src/com/clothingstore/BasicJavaFXApp.java
    if not errorlevel 1 (
        echo ✅ JavaFX 11 compilation successful
        set JAVAFX_VERSION=11
        goto create_launcher
    )
)

echo ❌ JavaFX compilation failed with both versions
echo The system will use Swing GUI instead
goto end

:create_launcher
echo.
echo Creating optimized JavaFX launcher...

REM Create launcher for the working JavaFX version
if "%JAVAFX_VERSION%"=="17" (
    set JAVAFX_PATH=javafx-sdk-17.0.2
) else (
    set JAVAFX_PATH=javafx-sdk-11.0.2
)

echo @echo off > Launch-JavaFX-Optimized.bat
echo title Clothing Store POS System - JavaFX %JAVAFX_VERSION% >> Launch-JavaFX-Optimized.bat
echo echo ======================================== >> Launch-JavaFX-Optimized.bat
echo echo  CLOTHING STORE POS SYSTEM - JavaFX %JAVAFX_VERSION% >> Launch-JavaFX-Optimized.bat
echo echo ======================================== >> Launch-JavaFX-Optimized.bat
echo echo. >> Launch-JavaFX-Optimized.bat
echo echo Starting optimized JavaFX GUI... >> Launch-JavaFX-Optimized.bat
echo echo. >> Launch-JavaFX-Optimized.bat
echo cd /d "%%~dp0" >> Launch-JavaFX-Optimized.bat
echo echo Launching with JavaFX %JAVAFX_VERSION% runtime... >> Launch-JavaFX-Optimized.bat
echo java -cp "bin;lib/sqlite-jdbc-********.jar" --module-path "%JAVAFX_PATH%/lib" --add-modules javafx.controls,javafx.fxml com.clothingstore.BasicJavaFXApp >> Launch-JavaFX-Optimized.bat
echo if errorlevel 1 ^( >> Launch-JavaFX-Optimized.bat
echo     echo JavaFX launch failed. Starting Swing POS as fallback... >> Launch-JavaFX-Optimized.bat
echo     java -cp "bin;lib/sqlite-jdbc-********.jar" com.clothingstore.gui.SwingPOSDemo >> Launch-JavaFX-Optimized.bat
echo ^) >> Launch-JavaFX-Optimized.bat
echo pause >> Launch-JavaFX-Optimized.bat

echo ✅ Optimized launcher created: Launch-JavaFX-Optimized.bat

:end
echo.
echo ========================================
echo  JAVAFX INSTALLATION COMPLETE!
echo ========================================
echo.
echo Installation Summary:
if exist "javafx-sdk-17.0.2\lib" (
    echo ✅ JavaFX 17 SDK: Installed and ready
)
if exist "javafx-sdk-11.0.2\lib" (
    echo ✅ JavaFX 11 SDK: Available as fallback
)
echo ✅ SQLite Database: Ready
echo ✅ Swing GUI: Always available as backup
echo.
echo Available Launch Options:
echo 1. Launch-JavaFX-Optimized.bat  - Best JavaFX experience
echo 2. Launch-POS-GUI.bat           - Reliable Swing interface
echo 3. Launch-Console-Demo.bat      - Console interface
echo 4. START-HERE.bat               - Main menu with all options
echo.
echo Recommended: Use Launch-JavaFX-Optimized.bat for the best experience!
echo.
pause

package com.clothingstore.test;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.ValidationUtil;
import com.clothingstore.util.FormatUtil;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Comprehensive test suite for all system components
 */
public class ComprehensiveTestSuite {
    
    private static int testsRun = 0;
    private static int testsPassed = 0;
    private static int testsFailed = 0;
    
    public static void main(String[] args) {
        System.out.println("CLOTHING STORE MANAGEMENT SYSTEM - COMPREHENSIVE TEST SUITE");
        System.out.println("===========================================================");
        
        try {
            // Initialize database
            DatabaseManager.getInstance().initializeDatabase();
            
            // Run all test categories
            testDatabaseOperations();
            testBusinessLogic();
            testValidationUtils();
            testFormatUtils();
            testTransactionWorkflows();
            testCustomerLoyaltyProgram();
            testInventoryManagement();
            testErrorHandling();
            
            // Print final results
            printTestResults();
            
        } catch (Exception e) {
            System.err.println("CRITICAL ERROR: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
        }
    }
    
    private static void testDatabaseOperations() {
        System.out.println("\n=== DATABASE OPERATIONS TESTS ===");
        
        try {
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            
            // Test product operations
            assertTest("Product findAll", () -> {
                List<Product> products = productDAO.findAll();
                return products.size() > 0;
            });
            
            assertTest("Product search", () -> {
                List<Product> results = productDAO.searchProducts("shirt");
                return results.size() > 0;
            });
            
            assertTest("Product categories", () -> {
                List<String> categories = productDAO.getAllCategories();
                return categories.contains("T-Shirts");
            });
            
            // Test customer operations
            assertTest("Customer findAll", () -> {
                List<Customer> customers = customerDAO.findAll();
                return customers.size() > 0;
            });
            
            assertTest("Customer search", () -> {
                List<Customer> results = customerDAO.searchCustomers("john");
                return results.size() > 0;
            });
            
            assertTest("Top customers", () -> {
                List<Customer> topCustomers = customerDAO.findTopCustomers(5);
                return topCustomers.size() > 0;
            });
            
            // Test transaction operations
            assertTest("Transaction findAll", () -> {
                try {
                    transactionDAO.findAll(); // May be empty initially
                    return true;
                } catch (SQLException e) {
                    return false;
                }
            });

        } catch (Exception e) {
            failTest("Database operations", e.getMessage());
        }
    }
    
    private static void testBusinessLogic() {
        System.out.println("\n=== BUSINESS LOGIC TESTS ===");
        
        try {
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            
            // Test product business logic
            List<Product> products = productDAO.findAll();
            if (!products.isEmpty()) {
                Product product = products.get(0);
                
                assertTest("Product profit calculation", () -> {
                    return product.getProfit() != null;
                });
                
                assertTest("Product low stock detection", () -> {
                    return product.isLowStock() == (product.getStockQuantity() <= product.getMinStockLevel());
                });
            }
            
            // Test customer business logic
            List<Customer> customers = customerDAO.findAll();
            if (!customers.isEmpty()) {
                Customer customer = customers.get(0);
                
                assertTest("Customer full name", () -> {
                    return customer.getFullName().contains(customer.getFirstName());
                });
                
                assertTest("Customer discount calculation", () -> {
                    double discount = customer.getDiscountPercentage();
                    return discount >= 0.0 && discount <= 1.0;
                });
                
                assertTest("Customer membership level", () -> {
                    String level = customer.getMembershipLevel();
                    return level.equals("BRONZE") || level.equals("SILVER") || 
                           level.equals("GOLD") || level.equals("PLATINUM");
                });
            }
            
        } catch (SQLException e) {
            failTest("Business logic", e.getMessage());
        }
    }
    
    private static void testValidationUtils() {
        System.out.println("\n=== VALIDATION UTILITIES TESTS ===");
        
        // Test email validation
        assertTest("Valid email", () -> ValidationUtil.isValidEmail("<EMAIL>"));
        assertTest("Invalid email", () -> !ValidationUtil.isValidEmail("invalid-email"));
        
        // Test phone validation
        assertTest("Valid phone", () -> ValidationUtil.isValidPhone("************"));
        assertTest("Invalid phone", () -> !ValidationUtil.isValidPhone("123"));
        
        // Test SKU validation
        assertTest("Valid SKU", () -> ValidationUtil.isValidSku("TSH001"));
        assertTest("Invalid SKU", () -> !ValidationUtil.isValidSku("INVALID"));
        
        // Test numeric validation
        assertTest("Positive BigDecimal", () -> ValidationUtil.isPositive(new BigDecimal("10.50")));
        assertTest("Negative BigDecimal", () -> !ValidationUtil.isPositive(new BigDecimal("-5.00")));
        
        // Test string validation
        assertTest("Non-empty string", () -> ValidationUtil.isNotEmpty("test"));
        assertTest("Empty string", () -> !ValidationUtil.isNotEmpty(""));
        assertTest("Null string", () -> !ValidationUtil.isNotEmpty(null));
        
        // Test product validation
        assertTest("Product name validation", () -> {
            ValidationUtil.ValidationResult result = ValidationUtil.validateProductName("Test Product");
            return result.isValid();
        });

        assertTest("Product price validation", () -> {
            ValidationUtil.ValidationResult result = ValidationUtil.validateProductPrice("19.99");
            return result.isValid();
        });

        assertTest("Stock quantity validation", () -> {
            ValidationUtil.ValidationResult result = ValidationUtil.validateStockQuantity("50");
            return result.isValid();
        });
    }
    
    private static void testFormatUtils() {
        System.out.println("\n=== FORMAT UTILITIES TESTS ===");
        
        // Test currency formatting
        assertTest("Currency formatting", () -> {
            String formatted = FormatUtil.formatCurrency(new BigDecimal("19.99"));
            return formatted.contains("19.99");
        });
        
        // Test phone formatting
        assertTest("Phone formatting", () -> {
            String formatted = FormatUtil.formatPhone("5551234567");
            return formatted.contains("(555)") || formatted.contains("555");
        });
        
        // Test name formatting
        assertTest("Name formatting", () -> {
            String formatted = FormatUtil.formatName("john doe");
            return formatted.equals("John Doe");
        });
        
        // Test date formatting
        assertTest("Date formatting", () -> {
            String formatted = FormatUtil.formatDate(LocalDate.now());
            return formatted.length() > 0;
        });
        
        // Test percentage formatting
        assertTest("Percentage formatting", () -> {
            String formatted = FormatUtil.formatPercentage(0.15);
            return formatted.contains("15");
        });
    }
    
    private static void testTransactionWorkflows() {
        System.out.println("\n=== TRANSACTION WORKFLOW TESTS ===");
        
        try {
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            TransactionService transactionService = TransactionService.getInstance();
            
            List<Product> products = productDAO.findAll();
            List<Customer> customers = customerDAO.findAll();
            
            if (!products.isEmpty() && !customers.isEmpty()) {
                Product product = products.get(0);
                Customer customer = customers.get(0);
                
                // Test transaction creation
                assertTest("Transaction creation", () -> {
                    Transaction transaction = new Transaction();
                    transaction.setCustomer(customer);
                    transaction.addItem(new TransactionItem(product, 1));
                    transaction.recalculateAmounts();
                    return transaction.getTotalAmount().compareTo(BigDecimal.ZERO) > 0;
                });
                
                // Test transaction number generation
                assertTest("Transaction number generation", () -> {
                    TransactionDAO transactionDAO = TransactionDAO.getInstance();
                    String txnNumber = transactionDAO.generateTransactionNumber();
                    return txnNumber.startsWith("TXN");
                });
            }
            
        } catch (SQLException e) {
            failTest("Transaction workflows", e.getMessage());
        }
    }
    
    private static void testCustomerLoyaltyProgram() {
        System.out.println("\n=== CUSTOMER LOYALTY PROGRAM TESTS ===");
        
        try {
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            List<Customer> customers = customerDAO.findAll();
            
            if (!customers.isEmpty()) {
                Customer customer = customers.get(0);
                int originalPoints = customer.getLoyaltyPoints();
                
                // Test loyalty point addition
                assertTest("Loyalty point addition", () -> {
                    customer.addLoyaltyPoints(100);
                    return customer.getLoyaltyPoints() == originalPoints + 100;
                });
                
                // Test loyalty point redemption
                assertTest("Loyalty point redemption", () -> {
                    boolean redeemed = customer.redeemLoyaltyPoints(50);
                    return redeemed && customer.getLoyaltyPoints() == originalPoints + 50;
                });
                
                // Test membership level logic
                assertTest("Membership level calculation", () -> {
                    String level = customer.getMembershipLevel();
                    double spent = customer.getTotalSpent();
                    
                    if (spent >= 5000) return level.equals("PLATINUM");
                    if (spent >= 2000) return level.equals("GOLD");
                    if (spent >= 500) return level.equals("SILVER");
                    return level.equals("BRONZE");
                });
            }
            
        } catch (SQLException e) {
            failTest("Customer loyalty program", e.getMessage());
        }
    }
    
    private static void testInventoryManagement() {
        System.out.println("\n=== INVENTORY MANAGEMENT TESTS ===");
        
        try {
            ProductDAO productDAO = ProductDAO.getInstance();
            List<Product> products = productDAO.findAll();
            
            if (!products.isEmpty()) {
                Product product = products.get(0);
                int originalStock = product.getStockQuantity();
                
                // Test stock reduction
                assertTest("Stock reduction", () -> {
                    product.reduceStock(1);
                    return product.getStockQuantity() == originalStock - 1;
                });
                
                // Test stock addition
                assertTest("Stock addition", () -> {
                    product.addStock(1);
                    return product.getStockQuantity() == originalStock;
                });
                
                // Test low stock detection
                List<Product> lowStockProducts = productDAO.findLowStockProducts();
                assertTest("Low stock detection", () -> {
                    return lowStockProducts.stream().allMatch(Product::isLowStock);
                });
            }
            
        } catch (SQLException e) {
            failTest("Inventory management", e.getMessage());
        }
    }
    
    private static void testErrorHandling() {
        System.out.println("\n=== ERROR HANDLING TESTS ===");
        
        try {
            ProductDAO productDAO = ProductDAO.getInstance();
            List<Product> products = productDAO.findAll();
            
            if (!products.isEmpty()) {
                Product product = products.get(0);
                
                // Test insufficient stock error
                assertTest("Insufficient stock error", () -> {
                    try {
                        product.reduceStock(product.getStockQuantity() + 1);
                        return false; // Should have thrown exception
                    } catch (IllegalArgumentException e) {
                        return true; // Expected exception
                    }
                });
            }
            
            // Test validation errors
            assertTest("Validation error handling", () -> {
                ValidationUtil.ValidationResult result = ValidationUtil.validateProductPrice("-10.00");
                return !result.isValid();
            });
            
        } catch (SQLException e) {
            failTest("Error handling", e.getMessage());
        }
    }
    
    private static void assertTest(String testName, TestCase testCase) {
        testsRun++;
        try {
            if (testCase.run()) {
                testsPassed++;
                System.out.println("PASS: " + testName);
            } else {
                testsFailed++;
                System.out.println("FAIL: " + testName + " - FAILED");
            }
        } catch (Exception e) {
            testsFailed++;
            System.out.println("ERROR: " + testName + " - ERROR: " + e.getMessage());
        }
    }
    
    private static void failTest(String testName, String reason) {
        testsRun++;
        testsFailed++;
        System.out.println("FAIL: " + testName + " - FAILED: " + reason);
    }
    
    private static void printTestResults() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("TEST RESULTS SUMMARY");
        System.out.println("=".repeat(60));
        System.out.println("Total Tests Run: " + testsRun);
        System.out.println("Tests Passed: " + testsPassed);
        System.out.println("Tests Failed: " + testsFailed);
        System.out.println("Success Rate: " + String.format("%.1f%%", (double) testsPassed / testsRun * 100));
        
        if (testsFailed == 0) {
            System.out.println("\nSUCCESS: ALL TESTS PASSED! System is ready for production.");
        } else {
            System.out.println("\nWARNING: Some tests failed. Please review and fix issues.");
        }
    }
    
    @FunctionalInterface
    private interface TestCase {
        boolean run() throws Exception;
    }
}

package com.clothingstore.util;

import java.util.Optional;
import java.util.Scanner;

/**
 * Console-based utility class for showing alerts and dialogs (for testing without JavaFX)
 */
public class ConsoleAlertUtil {
    
    private static Scanner scanner = new Scanner(System.in);

    /**
     * Shows an error message
     */
    public static void showError(String title, String message) {
        System.err.println("ERROR - " + title + ": " + message);
    }

    /**
     * Shows an information message
     */
    public static void showInfo(String title, String message) {
        System.out.println("INFO - " + title + ": " + message);
    }

    /**
     * Shows a warning message
     */
    public static void showWarning(String title, String message) {
        System.out.println("WARNING - " + title + ": " + message);
    }

    /**
     * Shows a confirmation dialog and returns true if user confirms
     */
    public static boolean showConfirmation(String title, String message) {
        System.out.print("CONFIRM - " + title + ": " + message + " (y/n): ");
        String response = scanner.nextLine().trim().toLowerCase();
        return response.equals("y") || response.equals("yes");
    }

    /**
     * Shows a confirmation dialog with custom button text
     */
    public static boolean showConfirmation(String title, String message, String okButtonText, String cancelButtonText) {
        System.out.print("CONFIRM - " + title + ": " + message + " (" + okButtonText + "/" + cancelButtonText + "): ");
        String response = scanner.nextLine().trim().toLowerCase();
        return response.equals(okButtonText.toLowerCase()) || response.equals("y") || response.equals("yes");
    }

    /**
     * Shows a text input dialog
     */
    public static Optional<String> showTextInput(String title, String headerText, String promptText) {
        System.out.println("INPUT - " + title);
        if (headerText != null && !headerText.isEmpty()) {
            System.out.println(headerText);
        }
        System.out.print(promptText + ": ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? Optional.empty() : Optional.of(input);
    }

    /**
     * Shows a text input dialog with default value
     */
    public static Optional<String> showTextInput(String title, String headerText, String promptText, String defaultValue) {
        System.out.println("INPUT - " + title);
        if (headerText != null && !headerText.isEmpty()) {
            System.out.println(headerText);
        }
        System.out.print(promptText + " [" + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return Optional.of(input.isEmpty() ? defaultValue : input);
    }

    /**
     * Shows a success message
     */
    public static void showSuccess(String title, String message) {
        System.out.println("SUCCESS - " + title + ": " + message);
    }

    /**
     * Shows an exception with detailed error information
     */
    public static void showException(String title, String message, Exception exception) {
        System.err.println("EXCEPTION - " + title + ": " + message);
        System.err.println("Details: " + exception.getMessage());
        exception.printStackTrace();
    }
}

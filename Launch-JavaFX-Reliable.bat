@echo off
title Clothing Store POS System - Reliable JavaFX GUI
echo ========================================
echo  CLOTHING STORE POS SYSTEM - JavaFX
echo ========================================
echo.
echo Starting reliable JavaFX GUI application...
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM Check if JavaFX SDK exists
if not exist "javafx-sdk-11.0.2\lib" (
    echo ERROR: JavaFX SDK not found
    echo Please ensure JavaFX SDK is extracted in javafx-sdk-11.0.2 folder
    pause
    exit /b 1
)

REM Compile if needed
if not exist "bin\com\clothingstore\BasicJavaFXApp.class" (
    echo Compiling reliable JavaFX application...
    javac -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" --module-path "javafx-sdk-11.0.2/lib" --add-modules javafx.controls,javafx.fxml -d bin src/com/clothingstore/BasicJavaFXApp.java
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo Compilation successful!
    echo.
)

echo Launching Reliable JavaFX Clothing Store POS System...
echo.
echo Features:
echo - Self-contained JavaFX interface (no external CSS dependencies)
echo - Robust error handling and fallback options
echo - Direct access to all POS system components
echo - Professional styling with inline CSS
echo - Reliable startup and operation
echo.
echo If the GUI doesn't appear, try the Swing version instead.
echo ========================================
echo.

REM Try to launch the reliable JavaFX application
echo Attempting to start JavaFX application...
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" --module-path "javafx-sdk-11.0.2/lib" --add-modules javafx.controls,javafx.fxml com.clothingstore.BasicJavaFXApp

if errorlevel 1 (
    echo.
    echo JavaFX launch failed. Trying alternative approach...
    echo.
    
    REM Fallback: Try launching Swing GUI instead
    echo Launching Swing POS as fallback...
    java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.gui.SwingPOSDemo
    
    if errorlevel 1 (
        echo.
        echo Both GUI attempts failed. Running console demo...
        java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.demo.SimplePOSDemo
    )
)

echo.
echo Application session ended.
pause

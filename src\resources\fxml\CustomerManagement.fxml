<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CustomerManagementController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Customer Management">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnAddCustomer" onAction="#handleAddCustomer" styleClass="button success" text="+ Add Customer" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Search and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-label" text="Search:" />
            <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" prefWidth="200.0" promptText="Search customers..." styleClass="search-box" />
            <Label styleClass="form-label" text="Membership:" />
            <ComboBox fx:id="cmbMembership" onAction="#handleMembershipFilter" prefWidth="120.0" promptText="All Levels" />
            <Label styleClass="form-label" text="Status:" />
            <ComboBox fx:id="cmbStatus" onAction="#handleStatusFilter" prefWidth="100.0" promptText="All" />
            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" styleClass="button secondary" text="Clear Filters" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Customer Statistics -->
      <HBox spacing="20.0" styleClass="form-container">
         <children>
            <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card">
               <children>
                  <Label styleClass="title" text="Total Customers" />
                  <Label fx:id="lblTotalCustomers" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card">
               <children>
                  <Label styleClass="title" text="Active Members" />
                  <Label fx:id="lblActiveMembers" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card">
               <children>
                  <Label styleClass="title" text="Total Loyalty Points" />
                  <Label fx:id="lblTotalPoints" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card">
               <children>
                  <Label styleClass="title" text="Average Spent" />
                  <Label fx:id="lblAverageSpent" styleClass="value" text="$0.00" />
               </children>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Customers Table -->
      <TableView fx:id="tblCustomers" styleClass="data-grid" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colName" prefWidth="150.0" text="Name" />
            <TableColumn fx:id="colEmail" prefWidth="180.0" text="Email" />
            <TableColumn fx:id="colPhone" prefWidth="120.0" text="Phone" />
            <TableColumn fx:id="colMembership" prefWidth="100.0" text="Membership" />
            <TableColumn fx:id="colPoints" prefWidth="80.0" text="Points" />
            <TableColumn fx:id="colTotalSpent" prefWidth="100.0" text="Total Spent" />
            <TableColumn fx:id="colTotalPurchases" prefWidth="80.0" text="Purchases" />
            <TableColumn fx:id="colLastPurchase" prefWidth="120.0" text="Last Purchase" />
            <TableColumn fx:id="colStatus" prefWidth="80.0" text="Status" />
            <TableColumn fx:id="colActions" prefWidth="120.0" text="Actions" />
         </columns>
         <contextMenu>
            <ContextMenu>
               <items>
                  <MenuItem fx:id="menuEdit" onAction="#handleEditCustomer" text="Edit Customer" />
                  <MenuItem fx:id="menuViewHistory" onAction="#handleViewHistory" text="View Purchase History" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuAdjustPoints" onAction="#handleAdjustPoints" text="Adjust Loyalty Points" />
                  <MenuItem fx:id="menuSendEmail" onAction="#handleSendEmail" text="Send Email" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuDeactivate" onAction="#handleDeactivateCustomer" text="Deactivate Customer" />
               </items>
            </ContextMenu>
         </contextMenu>
      </TableView>

      <!-- Summary Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="form-container">
         <children>
            <Label fx:id="lblFilteredCount" styleClass="form-label" text="Showing: 0 customers" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
            <Button fx:id="btnLoyaltyReport" onAction="#handleLoyaltyReport" styleClass="button warning" text="🏆 Loyalty Report" />
            <Button fx:id="btnBirthdayReport" onAction="#handleBirthdayReport" text="🎂 Birthdays" />
         </children>
         <VBox.margin>
            <Insets top="10.0" />
         </VBox.margin>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>

package com.clothingstore.database;

import java.sql.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Database Manager class for handling SQLite database connections and initialization
 */
public class DatabaseManager {
    private static DatabaseManager instance;
    private static final String DATABASE_NAME = "clothing_store.db";
    private static final String DATABASE_URL = "jdbc:sqlite:" + DATABASE_NAME;
    private Connection connection;

    private DatabaseManager() {}

    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }

    public Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            try {
                Class.forName("org.sqlite.JDBC");
                connection = DriverManager.getConnection(DATABASE_URL);
                connection.setAutoCommit(true);
            } catch (ClassNotFoundException e) {
                throw new SQLException("SQLite JDBC driver not found", e);
            }
        }
        return connection;
    }

    public void initializeDatabase() throws SQLException {
        try (Connection conn = getConnection()) {
            createTables(conn);
            insertSampleData(conn);
        }
    }

    private void createTables(Connection conn) throws SQLException {
        // Create Products table
        String createProductsTable = """
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sku TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                brand TEXT,
                color TEXT,
                size TEXT,
                price DECIMAL(10,2) NOT NULL,
                cost_price DECIMAL(10,2),
                stock_quantity INTEGER NOT NULL DEFAULT 0,
                min_stock_level INTEGER DEFAULT 5,
                image_url TEXT,
                active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;

        // Create Customers table
        String createCustomersTable = """
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                email TEXT UNIQUE,
                phone TEXT,
                address TEXT,
                city TEXT,
                state TEXT,
                zip_code TEXT,
                date_of_birth DATE,
                gender TEXT,
                registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1,
                loyalty_points INTEGER DEFAULT 0,
                membership_level TEXT DEFAULT 'BRONZE',
                last_purchase_date DATETIME,
                total_spent DECIMAL(10,2) DEFAULT 0.00,
                total_purchases INTEGER DEFAULT 0
            )
        """;

        // Create Transactions table
        String createTransactionsTable = """
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                subtotal DECIMAL(10,2) NOT NULL,
                tax_amount DECIMAL(10,2) DEFAULT 0.00,
                discount_amount DECIMAL(10,2) DEFAULT 0.00,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_method TEXT NOT NULL,
                status TEXT DEFAULT 'COMPLETED',
                notes TEXT,
                cashier_name TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )
        """;

        // Create Transaction Items table
        String createTransactionItemsTable = """
            CREATE TABLE IF NOT EXISTS transaction_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                line_total DECIMAL(10,2) NOT NULL,
                discount_amount DECIMAL(10,2) DEFAULT 0.00,
                notes TEXT,
                FOREIGN KEY (transaction_id) REFERENCES transactions(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """;

        // Create indexes for better performance
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
            "CREATE INDEX IF NOT EXISTS idx_products_active ON products(active)",
            "CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id)"
        };

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(createProductsTable);
            stmt.execute(createCustomersTable);
            stmt.execute(createTransactionsTable);
            stmt.execute(createTransactionItemsTable);
            
            for (String index : indexes) {
                stmt.execute(index);
            }
        }
    }

    private void insertSampleData(Connection conn) throws SQLException {
        // Check if sample data already exists
        String checkQuery = "SELECT COUNT(*) FROM products";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(checkQuery)) {
            if (rs.next() && rs.getInt(1) > 0) {
                return; // Sample data already exists
            }
        }

        // Insert sample products
        String insertProduct = """
            INSERT INTO products (sku, name, description, category, brand, color, size, price, cost_price, stock_quantity, min_stock_level)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;

        try (PreparedStatement pstmt = conn.prepareStatement(insertProduct)) {
            // Sample clothing items
            Object[][] sampleProducts = {
                {"TSH001", "Classic Cotton T-Shirt", "Comfortable cotton t-shirt", "T-Shirts", "BasicWear", "White", "M", 19.99, 8.00, 50, 10},
                {"TSH002", "Classic Cotton T-Shirt", "Comfortable cotton t-shirt", "T-Shirts", "BasicWear", "Black", "M", 19.99, 8.00, 45, 10},
                {"TSH003", "Classic Cotton T-Shirt", "Comfortable cotton t-shirt", "T-Shirts", "BasicWear", "Blue", "L", 19.99, 8.00, 30, 10},
                {"JNS001", "Slim Fit Jeans", "Modern slim fit denim jeans", "Jeans", "DenimCo", "Dark Blue", "32", 79.99, 35.00, 25, 5},
                {"JNS002", "Slim Fit Jeans", "Modern slim fit denim jeans", "Jeans", "DenimCo", "Light Blue", "34", 79.99, 35.00, 20, 5},
                {"DRS001", "Summer Dress", "Light and airy summer dress", "Dresses", "SummerStyle", "Floral", "S", 59.99, 25.00, 15, 3},
                {"DRS002", "Summer Dress", "Light and airy summer dress", "Dresses", "SummerStyle", "Solid Blue", "M", 59.99, 25.00, 12, 3},
                {"SHT001", "Button-Down Shirt", "Professional button-down shirt", "Shirts", "OfficePro", "White", "L", 49.99, 20.00, 18, 5},
                {"SHT002", "Button-Down Shirt", "Professional button-down shirt", "Shirts", "OfficePro", "Light Blue", "M", 49.99, 20.00, 22, 5},
                {"JKT001", "Leather Jacket", "Genuine leather jacket", "Jackets", "LeatherLux", "Black", "L", 199.99, 80.00, 8, 2}
            };

            for (Object[] product : sampleProducts) {
                for (int i = 0; i < product.length; i++) {
                    pstmt.setObject(i + 1, product[i]);
                }
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }

        // Insert sample customers
        String insertCustomer = """
            INSERT INTO customers (first_name, last_name, email, phone, address, city, state, zip_code, loyalty_points, membership_level, total_spent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;

        try (PreparedStatement pstmt = conn.prepareStatement(insertCustomer)) {
            Object[][] sampleCustomers = {
                {"John", "Doe", "<EMAIL>", "555-0101", "123 Main St", "Anytown", "CA", "12345", 150, "SILVER", 750.00},
                {"Jane", "Smith", "<EMAIL>", "555-0102", "456 Oak Ave", "Somewhere", "NY", "67890", 50, "BRONZE", 200.00},
                {"Mike", "Johnson", "<EMAIL>", "555-0103", "789 Pine Rd", "Elsewhere", "TX", "54321", 300, "GOLD", 1500.00}
            };

            for (Object[] customer : sampleCustomers) {
                for (int i = 0; i < customer.length; i++) {
                    pstmt.setObject(i + 1, customer[i]);
                }
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }
    }

    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            System.err.println("Error closing database connection: " + e.getMessage());
        }
    }

    public void backupDatabase(String backupPath) throws SQLException, IOException {
        if (!Files.exists(Paths.get(DATABASE_NAME))) {
            throw new IOException("Database file does not exist");
        }
        Files.copy(Paths.get(DATABASE_NAME), Paths.get(backupPath));
    }

    public void restoreDatabase(String backupPath) throws SQLException, IOException {
        closeConnection();
        if (!Files.exists(Paths.get(backupPath))) {
            throw new IOException("Backup file does not exist");
        }
        Files.copy(Paths.get(backupPath), Paths.get(DATABASE_NAME));
        // Reinitialize connection
        connection = null;
        getConnection();
    }
}

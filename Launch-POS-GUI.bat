@echo off
title Clothing Store POS System - GUI Launcher
echo ========================================
echo  CLOTHING STORE POS SYSTEM - GUI
echo ========================================
echo.
echo Starting GUI application...
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "bin\com\clothingstore\gui\SwingPOSDemo.class" (
    echo Compiling application...
    javac -cp "lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java src/com/clothingstore/gui/SwingPOSDemo.java
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo Compilation successful!
    echo.
)

echo Launching Clothing Store POS System...
echo.
echo Features:
echo - Point of Sale with manual discount controls
echo - Customer management and loyalty program
echo - Real-time inventory tracking
echo - Professional GUI interface
echo.
echo Close this window to exit the application.
echo ========================================
echo.

REM Launch the GUI application
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.gui.SwingPOSDemo

echo.
echo Application closed.
pause

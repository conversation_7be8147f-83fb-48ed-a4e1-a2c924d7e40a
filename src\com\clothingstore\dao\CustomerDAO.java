package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Data Access Object for Customer operations
 */
public class CustomerDAO {
    
    private static CustomerDAO instance;
    
    private CustomerDAO() {}
    
    public static synchronized CustomerDAO getInstance() {
        if (instance == null) {
            instance = new CustomerDAO();
        }
        return instance;
    }
    
    public List<Customer> findAll() throws SQLException {
        String sql = "SELECT * FROM customers WHERE active = 1 ORDER BY last_name, first_name";
        List<Customer> customers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                customers.add(mapResultSetToCustomer(rs));
            }
        }
        
        return customers;
    }
    
    public Optional<Customer> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM customers WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    public Optional<Customer> findByEmail(String email) throws SQLException {
        String sql = "SELECT * FROM customers WHERE email = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, email);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    public Optional<Customer> findByPhone(String phone) throws SQLException {
        String sql = "SELECT * FROM customers WHERE phone = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, phone);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    public List<Customer> searchCustomers(String searchTerm) throws SQLException {
        String sql = """
            SELECT * FROM customers 
            WHERE active = 1 AND (
                first_name LIKE ? OR 
                last_name LIKE ? OR 
                email LIKE ? OR 
                phone LIKE ?
            ) ORDER BY last_name, first_name
        """;
        
        List<Customer> customers = new ArrayList<>();
        String searchPattern = "%" + searchTerm + "%";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 1; i <= 4; i++) {
                pstmt.setString(i, searchPattern);
            }
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    customers.add(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return customers;
    }
    
    public List<Customer> findByMembershipLevel(String membershipLevel) throws SQLException {
        String sql = "SELECT * FROM customers WHERE membership_level = ? AND active = 1 ORDER BY total_spent DESC";
        List<Customer> customers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, membershipLevel);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    customers.add(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return customers;
    }
    
    public List<Customer> findTopCustomers(int limit) throws SQLException {
        String sql = "SELECT * FROM customers WHERE active = 1 ORDER BY total_spent DESC LIMIT ?";
        List<Customer> customers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    customers.add(mapResultSetToCustomer(rs));
                }
            }
        }
        
        return customers;
    }
    
    public Customer save(Customer customer) throws SQLException {
        if (customer.getId() == null) {
            return insert(customer);
        } else {
            return update(customer);
        }
    }
    
    private Customer insert(Customer customer) throws SQLException {
        String sql = """
            INSERT INTO customers (first_name, last_name, email, phone, address, city, state, zip_code,
                                 date_of_birth, gender, active, loyalty_points, membership_level, 
                                 total_spent, total_purchases)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            setPreparedStatementParameters(pstmt, customer);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating customer failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    customer.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating customer failed, no ID obtained.");
                }
            }
        }
        
        return customer;
    }
    
    private Customer update(Customer customer) throws SQLException {
        String sql = """
            UPDATE customers SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, 
                               city = ?, state = ?, zip_code = ?, date_of_birth = ?, gender = ?, 
                               active = ?, loyalty_points = ?, membership_level = ?, 
                               last_purchase_date = ?, total_spent = ?, total_purchases = ?
            WHERE id = ?
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            setPreparedStatementParameters(pstmt, customer);
            pstmt.setTimestamp(16, customer.getLastPurchaseDate() != null ? 
                Timestamp.valueOf(customer.getLastPurchaseDate()) : null);
            pstmt.setLong(17, customer.getId());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating customer failed, no rows affected.");
            }
        }
        
        return customer;
    }
    
    public void delete(Long id) throws SQLException {
        // Soft delete - mark as inactive
        String sql = "UPDATE customers SET active = 0 WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Deleting customer failed, no rows affected.");
            }
        }
    }
    
    public void updateLoyaltyPoints(Long customerId, int points) throws SQLException {
        String sql = "UPDATE customers SET loyalty_points = ? WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, points);
            pstmt.setLong(2, customerId);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating loyalty points failed, no rows affected.");
            }
        }
    }
    
    public void updatePurchaseHistory(Long customerId, double amount) throws SQLException {
        String sql = """
            UPDATE customers 
            SET total_spent = total_spent + ?, 
                total_purchases = total_purchases + 1,
                last_purchase_date = CURRENT_TIMESTAMP,
                loyalty_points = loyalty_points + ?
            WHERE id = ?
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setDouble(1, amount);
            pstmt.setInt(2, (int) amount); // 1 point per dollar
            pstmt.setLong(3, customerId);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating purchase history failed, no rows affected.");
            }
            
            // Update membership level based on total spent
            updateMembershipLevel(customerId);
        }
    }
    
    private void updateMembershipLevel(Long customerId) throws SQLException {
        String sql = """
            UPDATE customers 
            SET membership_level = CASE 
                WHEN total_spent >= 5000 THEN 'PLATINUM'
                WHEN total_spent >= 2000 THEN 'GOLD'
                WHEN total_spent >= 500 THEN 'SILVER'
                ELSE 'BRONZE'
            END
            WHERE id = ?
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, customerId);
            pstmt.executeUpdate();
        }
    }
    
    private void setPreparedStatementParameters(PreparedStatement pstmt, Customer customer) throws SQLException {
        pstmt.setString(1, customer.getFirstName());
        pstmt.setString(2, customer.getLastName());
        pstmt.setString(3, customer.getEmail());
        pstmt.setString(4, customer.getPhone());
        pstmt.setString(5, customer.getAddress());
        pstmt.setString(6, customer.getCity());
        pstmt.setString(7, customer.getState());
        pstmt.setString(8, customer.getZipCode());
        pstmt.setDate(9, customer.getDateOfBirth() != null ? Date.valueOf(customer.getDateOfBirth()) : null);
        pstmt.setString(10, customer.getGender());
        pstmt.setBoolean(11, customer.isActive());
        pstmt.setInt(12, customer.getLoyaltyPoints());
        pstmt.setString(13, customer.getMembershipLevel());
        pstmt.setDouble(14, customer.getTotalSpent());
        pstmt.setInt(15, customer.getTotalPurchases());
    }
    
    private Customer mapResultSetToCustomer(ResultSet rs) throws SQLException {
        Customer customer = new Customer();
        customer.setId(rs.getLong("id"));
        customer.setFirstName(rs.getString("first_name"));
        customer.setLastName(rs.getString("last_name"));
        customer.setEmail(rs.getString("email"));
        customer.setPhone(rs.getString("phone"));
        customer.setAddress(rs.getString("address"));
        customer.setCity(rs.getString("city"));
        customer.setState(rs.getString("state"));
        customer.setZipCode(rs.getString("zip_code"));
        
        Date dateOfBirth = rs.getDate("date_of_birth");
        if (dateOfBirth != null) {
            customer.setDateOfBirth(dateOfBirth.toLocalDate());
        }
        
        customer.setGender(rs.getString("gender"));
        
        Timestamp registrationDate = rs.getTimestamp("registration_date");
        if (registrationDate != null) {
            customer.setRegistrationDate(registrationDate.toLocalDateTime());
        }
        
        customer.setActive(rs.getBoolean("active"));
        customer.setLoyaltyPoints(rs.getInt("loyalty_points"));
        customer.setMembershipLevel(rs.getString("membership_level"));
        
        Timestamp lastPurchaseDate = rs.getTimestamp("last_purchase_date");
        if (lastPurchaseDate != null) {
            customer.setLastPurchaseDate(lastPurchaseDate.toLocalDateTime());
        }
        
        customer.setTotalSpent(rs.getDouble("total_spent"));
        customer.setTotalPurchases(rs.getInt("total_purchases"));
        
        return customer;
    }
}

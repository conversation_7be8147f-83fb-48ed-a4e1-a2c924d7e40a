<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PointOfSaleController">
   <top>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Point of Sale">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblTransactionNumber" styleClass="form-label" text="Transaction: TXN000000" />
            <Label fx:id="lblCashier" styleClass="form-label" text="Cashier: Admin" />
            <Button fx:id="btnNewTransaction" onAction="#handleNewTransaction" styleClass="button success" text="🔄 New Transaction" />
         </children>
         <BorderPane.margin>
            <Insets bottom="10.0" />
         </BorderPane.margin>
      </HBox>
   </top>
   
   <left>
      <!-- Product Search and Selection -->
      <VBox prefWidth="400.0" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Product Search">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            
            <!-- Search Controls -->
            <HBox spacing="10.0">
               <children>
                  <TextField fx:id="txtProductSearch" onKeyReleased="#handleProductSearch" prefWidth="200.0" promptText="Search products..." HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnScanBarcode" onAction="#handleScanBarcode" text="📷 Scan" />
               </children>
            </HBox>
            
            <HBox spacing="10.0">
               <children>
                  <ComboBox fx:id="cmbProductCategory" onAction="#handleCategoryFilter" prefWidth="150.0" promptText="Category" />
                  <Button fx:id="btnClearSearch" onAction="#handleClearSearch" styleClass="button secondary" text="Clear" />
               </children>
            </HBox>
            
            <!-- Product List -->
            <TableView fx:id="tblProducts" prefHeight="300.0" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="colProductSku" prefWidth="80.0" text="SKU" />
                  <TableColumn fx:id="colProductName" prefWidth="150.0" text="Product" />
                  <TableColumn fx:id="colProductPrice" prefWidth="70.0" text="Price" />
                  <TableColumn fx:id="colProductStock" prefWidth="60.0" text="Stock" />
                  <TableColumn fx:id="colProductAction" prefWidth="40.0" text="Add" />
               </columns>
            </TableView>
            
            <!-- Quick Add -->
            <HBox spacing="10.0">
               <children>
                  <TextField fx:id="txtQuickSku" onAction="#handleQuickAdd" promptText="Enter SKU..." HBox.hgrow="ALWAYS" />
                  <Spinner fx:id="spnQuickQuantity" prefWidth="80.0" />
                  <Button fx:id="btnQuickAdd" onAction="#handleQuickAdd" styleClass="button success" text="+ Add" />
               </children>
            </HBox>
         </children>
         <BorderPane.margin>
            <Insets right="10.0" />
         </BorderPane.margin>
      </VBox>
   </left>
   
   <center>
      <!-- Shopping Cart -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="form-title" text="Shopping Cart">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnClearCart" onAction="#handleClearCart" styleClass="button warning" text="🗑 Clear Cart" />
               </children>
            </HBox>
            
            <!-- Cart Items Table -->
            <TableView fx:id="tblCartItems" prefHeight="250.0" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="colCartProduct" prefWidth="200.0" text="Product" />
                  <TableColumn fx:id="colCartQuantity" prefWidth="80.0" text="Qty" />
                  <TableColumn fx:id="colCartUnitPrice" prefWidth="80.0" text="Unit Price" />
                  <TableColumn fx:id="colCartDiscount" prefWidth="80.0" text="Discount" />
                  <TableColumn fx:id="colCartTotal" prefWidth="80.0" text="Total" />
                  <TableColumn fx:id="colCartActions" prefWidth="80.0" text="Actions" />
               </columns>
            </TableView>
            
            <!-- Cart Summary -->
            <GridPane hgap="10.0" vgap="5.0" styleClass="form-container">
               <columnConstraints>
                  <ColumnConstraints />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               
               <Label styleClass="form-label" text="Subtotal:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
               <Label fx:id="lblSubtotal" styleClass="form-label" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
               
               <Label styleClass="form-label" text="Discount:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
               <HBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                  <children>
                     <Label fx:id="lblDiscount" styleClass="form-label" text="$0.00" />
                     <Button fx:id="btnApplyDiscount" onAction="#handleApplyDiscount" styleClass="button secondary" text="Apply" />
                     <Button fx:id="btnClearDiscount" onAction="#handleClearDiscount" styleClass="button secondary" text="Clear" />
                  </children>
               </HBox>
               
               <Separator GridPane.columnIndex="0" GridPane.columnSpan="2" GridPane.rowIndex="2" />

               <Label styleClass="form-title" text="Total:" GridPane.columnIndex="0" GridPane.rowIndex="3">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Label fx:id="lblTotal" styleClass="form-title" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="3">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
            </GridPane>
         </children>
      </VBox>
   </center>
   
   <right>
      <!-- Customer and Payment -->
      <VBox prefWidth="300.0" spacing="10.0" styleClass="form-container">
         <children>
            <!-- Customer Section -->
            <VBox spacing="10.0" styleClass="form-container">
               <children>
                  <Label styleClass="form-title" text="Customer">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <HBox spacing="10.0">
                     <children>
                        <TextField fx:id="txtCustomerSearch" onKeyReleased="#handleCustomerSearch" promptText="Search customer..." HBox.hgrow="ALWAYS" />
                        <Button fx:id="btnNewCustomer" onAction="#handleNewCustomer" text="+ New" />
                     </children>
                  </HBox>
                  
                  <VBox fx:id="customerInfo" spacing="5.0" visible="false">
                     <children>
                        <Label fx:id="lblCustomerName" styleClass="form-label" text="Customer Name" />
                        <Label fx:id="lblCustomerMembership" styleClass="form-label" text="Membership Level" />
                        <Label fx:id="lblCustomerPoints" styleClass="form-label" text="Loyalty Points: 0" />
                        <Button fx:id="btnRemoveCustomer" onAction="#handleRemoveCustomer" styleClass="button secondary" text="Remove Customer" />
                     </children>
                  </VBox>
               </children>
            </VBox>
            
            <!-- Payment Section -->
            <VBox spacing="10.0" styleClass="form-container">
               <children>
                  <Label styleClass="form-title" text="Payment">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <ComboBox fx:id="cmbPaymentMethod" prefWidth="200.0" promptText="Payment Method" />
                  
                  <HBox spacing="10.0">
                     <children>
                        <Label styleClass="form-label" text="Amount Received:" />
                        <TextField fx:id="txtAmountReceived" onKeyReleased="#handleAmountReceived" prefWidth="100.0" promptText="0.00" />
                     </children>
                  </HBox>
                  
                  <HBox spacing="10.0">
                     <children>
                        <Label styleClass="form-label" text="Change:" />
                        <Label fx:id="lblChange" styleClass="form-label" text="$0.00" />
                     </children>
                  </HBox>
                  
                  <TextArea fx:id="txtNotes" prefHeight="60.0" promptText="Transaction notes..." />
               </children>
            </VBox>
            
            <!-- Action Buttons -->
            <VBox spacing="10.0">
               <children>
                  <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" prefHeight="50.0" styleClass="button success" text="💳 Process Payment" />
                  <Button fx:id="btnHoldTransaction" onAction="#handleHoldTransaction" styleClass="button warning" text="⏸ Hold Transaction" />
                  <Button fx:id="btnVoidTransaction" onAction="#handleVoidTransaction" styleClass="button danger" text="❌ Void Transaction" />
               </children>
            </VBox>
         </children>
         <BorderPane.margin>
            <Insets left="10.0" />
         </BorderPane.margin>
      </VBox>
   </right>
   
   <bottom>
      <!-- Status Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="status-bar">
         <children>
            <Label fx:id="lblStatus" text="Ready for new transaction" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblItemCount" text="Items: 0" />
            <Label fx:id="lblTransactionTime" text="Time: --:--" />
         </children>
         <BorderPane.margin>
            <Insets top="10.0" />
         </BorderPane.margin>
      </HBox>
   </bottom>
</BorderPane>

package com.clothingstore;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.gui.SwingPOSDemo;
import com.clothingstore.demo.SimplePOSDemo;
import com.clothingstore.demo.ManualDiscountDemo;
import com.clothingstore.test.ComprehensiveTestSuite;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Simple JavaFX Application for Clothing Store POS System
 */
public class SimpleJavaFXApp extends Application {

    private Label statusLabel;
    private Label timeLabel;
    private Timer clockTimer;
    private TabPane mainTabPane;

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database
            DatabaseManager.getInstance().initializeDatabase();

            // Create main layout
            BorderPane root = createMainLayout();
            
            // Create scene
            Scene scene = new Scene(root, 1200, 800);
            scene.getStylesheets().add(getClass().getResource("/css/simple-style.css").toExternalForm());

            // Configure stage
            primaryStage.setTitle("Clothing Store POS & Inventory Management System");
            primaryStage.setScene(scene);
            primaryStage.setMaximized(true);
            primaryStage.setOnCloseRequest(e -> {
                if (clockTimer != null) clockTimer.cancel();
                Platform.exit();
            });

            // Show application
            primaryStage.show();
            
            // Start clock
            setupClock();
            
            statusLabel.setText("Clothing Store POS System Ready - Manual Discount Controls Active");

        } catch (Exception e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Application Startup Error");
            alert.setContentText("Failed to start the application: " + e.getMessage());
            alert.showAndWait();
            e.printStackTrace();
        }
    }

    private BorderPane createMainLayout() {
        BorderPane root = new BorderPane();
        
        // Top: Menu and Toolbar
        VBox topBox = new VBox();
        topBox.getChildren().addAll(createMenuBar(), createToolBar());
        root.setTop(topBox);
        
        // Center: Tab Pane
        mainTabPane = new TabPane();
        mainTabPane.setTabClosingPolicy(TabPane.TabClosingPolicy.SELECTED_TAB);
        
        // Add dashboard tab
        Tab dashboardTab = createDashboardTab();
        dashboardTab.setClosable(false);
        mainTabPane.getTabs().add(dashboardTab);
        
        root.setCenter(mainTabPane);
        
        // Bottom: Status bar
        root.setBottom(createStatusBar());
        
        return root;
    }

    private MenuBar createMenuBar() {
        MenuBar menuBar = new MenuBar();
        
        // File Menu
        Menu fileMenu = new Menu("File");
        MenuItem exitItem = new MenuItem("Exit");
        exitItem.setOnAction(e -> Platform.exit());
        fileMenu.getItems().add(exitItem);
        
        // Inventory Menu
        Menu inventoryMenu = new Menu("Inventory");
        MenuItem manageProductsItem = new MenuItem("Manage Products");
        manageProductsItem.setOnAction(e -> showInventoryTab());
        MenuItem lowStockItem = new MenuItem("Low Stock Report");
        lowStockItem.setOnAction(e -> statusLabel.setText("Low stock report available through inventory management"));
        inventoryMenu.getItems().addAll(manageProductsItem, lowStockItem);
        
        // Customers Menu
        Menu customersMenu = new Menu("Customers");
        MenuItem manageCustomersItem = new MenuItem("Manage Customers");
        manageCustomersItem.setOnAction(e -> showCustomersTab());
        MenuItem customerReportItem = new MenuItem("Customer Report");
        customerReportItem.setOnAction(e -> statusLabel.setText("Customer reports available through customer management"));
        customersMenu.getItems().addAll(manageCustomersItem, customerReportItem);
        
        // Sales Menu
        Menu salesMenu = new Menu("Sales");
        MenuItem posItem = new MenuItem("Point of Sale");
        posItem.setOnAction(e -> showPOSTab());
        MenuItem salesReportItem = new MenuItem("Sales Report");
        salesReportItem.setOnAction(e -> showReportsTab());
        salesMenu.getItems().addAll(posItem, salesReportItem);
        
        // Help Menu
        Menu helpMenu = new Menu("Help");
        MenuItem aboutItem = new MenuItem("About");
        aboutItem.setOnAction(e -> showAbout());
        helpMenu.getItems().add(aboutItem);
        
        menuBar.getMenus().addAll(fileMenu, inventoryMenu, customersMenu, salesMenu, helpMenu);
        return menuBar;
    }

    private ToolBar createToolBar() {
        ToolBar toolBar = new ToolBar();
        
        Button posBtn = new Button("POS System");
        posBtn.setOnAction(e -> showPOSTab());
        
        Button inventoryBtn = new Button("Inventory");
        inventoryBtn.setOnAction(e -> showInventoryTab());
        
        Button customersBtn = new Button("Customers");
        customersBtn.setOnAction(e -> showCustomersTab());
        
        Button reportsBtn = new Button("Reports");
        reportsBtn.setOnAction(e -> showReportsTab());
        
        toolBar.getItems().addAll(posBtn, new Separator(), inventoryBtn, customersBtn, new Separator(), reportsBtn);
        return toolBar;
    }

    private Tab createDashboardTab() {
        Tab tab = new Tab("Dashboard");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(30));
        content.setAlignment(Pos.CENTER);
        
        // Title
        Label title = new Label("Clothing Store POS & Inventory Management System");
        title.setFont(Font.font("System", FontWeight.BOLD, 24));
        
        Label subtitle = new Label("Manual Discount Control System");
        subtitle.setFont(Font.font("System", FontWeight.NORMAL, 16));
        subtitle.setStyle("-fx-text-fill: #666;");
        
        // Feature buttons grid
        GridPane buttonGrid = new GridPane();
        buttonGrid.setAlignment(Pos.CENTER);
        buttonGrid.setHgap(20);
        buttonGrid.setVgap(20);
        
        Button posBtn = createDashboardButton("Point of Sale", "Launch POS interface with manual discount controls");
        posBtn.setOnAction(e -> showPOSTab());
        
        Button inventoryBtn = createDashboardButton("Inventory Management", "Manage products and stock levels");
        inventoryBtn.setOnAction(e -> showInventoryTab());
        
        Button customersBtn = createDashboardButton("Customer Management", "Manage customer loyalty program");
        customersBtn.setOnAction(e -> showCustomersTab());
        
        Button reportsBtn = createDashboardButton("Reports & Analytics", "View business intelligence reports");
        reportsBtn.setOnAction(e -> showReportsTab());
        
        buttonGrid.add(posBtn, 0, 0);
        buttonGrid.add(inventoryBtn, 1, 0);
        buttonGrid.add(customersBtn, 0, 1);
        buttonGrid.add(reportsBtn, 1, 1);
        
        // Features list
        VBox features = new VBox(10);
        features.setAlignment(Pos.CENTER);
        
        Label featuresTitle = new Label("System Features:");
        featuresTitle.setFont(Font.font("System", FontWeight.BOLD, 14));
        
        features.getChildren().addAll(
            featuresTitle,
            new Label("✓ Manual Discount Controls - No Automatic Discounts"),
            new Label("✓ Real-time Inventory Tracking"),
            new Label("✓ Customer Loyalty Program Management"),
            new Label("✓ Comprehensive Reporting"),
            new Label("✓ Professional Point of Sale Interface")
        );
        
        content.getChildren().addAll(title, subtitle, new Separator(), buttonGrid, new Separator(), features);
        tab.setContent(new ScrollPane(content));
        
        return tab;
    }

    private Button createDashboardButton(String title, String description) {
        Button button = new Button(title);
        button.setPrefSize(200, 100);
        button.setFont(Font.font("System", FontWeight.NORMAL, 14));
        button.setTooltip(new Tooltip(description));
        return button;
    }

    private HBox createStatusBar() {
        HBox statusBar = new HBox(10);
        statusBar.setPadding(new Insets(5, 10, 5, 10));
        statusBar.setAlignment(Pos.CENTER_LEFT);
        statusBar.setStyle("-fx-background-color: #f0f0f0; -fx-border-color: #ccc; -fx-border-width: 1 0 0 0;");
        
        statusLabel = new Label("Ready");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        timeLabel = new Label("00:00:00");
        
        statusBar.getChildren().addAll(statusLabel, spacer, timeLabel);
        return statusBar;
    }

    private void setupClock() {
        clockTimer = new Timer(true);
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    timeLabel.setText(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        }, 0, 1000);
    }

    private void showPOSTab() {
        Tab posTab = findOrCreateTab("Point of Sale", "pos-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Point of Sale System");
        title.setFont(Font.font("System", FontWeight.BOLD, 24));
        
        Label subtitle = new Label("Manual Discount Control System");
        subtitle.setFont(Font.font("System", FontWeight.NORMAL, 16));
        subtitle.setStyle("-fx-text-fill: #666;");
        
        VBox features = new VBox(10);
        features.setAlignment(Pos.CENTER_LEFT);
        features.getChildren().addAll(
            new Label("✓ No automatic discounts - full cashier control"),
            new Label("✓ Manual percentage and dollar amount discounts"),
            new Label("✓ Real-time inventory updates"),
            new Label("✓ Customer loyalty integration"),
            new Label("✓ Professional transaction processing")
        );
        
        HBox buttons = new HBox(15);
        buttons.setAlignment(Pos.CENTER);
        
        Button swingPOSBtn = new Button("Launch Swing POS Interface");
        swingPOSBtn.setOnAction(e -> {
            new Thread(() -> SwingPOSDemo.main(new String[]{})).start();
            statusLabel.setText("Swing POS Interface Launched");
        });
        
        Button consoleDemoBtn = new Button("Run Console Demo");
        consoleDemoBtn.setOnAction(e -> {
            new Thread(() -> SimplePOSDemo.main(new String[]{})).start();
            statusLabel.setText("Console Demo Started");
        });
        
        Button discountDemoBtn = new Button("Manual Discount Demo");
        discountDemoBtn.setOnAction(e -> {
            new Thread(() -> ManualDiscountDemo.main(new String[]{})).start();
            statusLabel.setText("Manual Discount Demo Started");
        });
        
        buttons.getChildren().addAll(swingPOSBtn, consoleDemoBtn, discountDemoBtn);
        
        content.getChildren().addAll(title, subtitle, new Separator(), features, new Separator(), buttons);
        posTab.setContent(new ScrollPane(content));
        
        mainTabPane.getSelectionModel().select(posTab);
    }

    private void showInventoryTab() {
        Tab tab = findOrCreateTab("Inventory Management", "inventory-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Inventory Management");
        title.setFont(Font.font("System", FontWeight.BOLD, 24));
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Current Inventory Status:"),
            new Label("• 10 Products across 5 categories"),
            new Label("• Real-time stock tracking"),
            new Label("• Automatic low stock alerts"),
            new Label("• Category-based organization"),
            new Label("• Stock adjustment capabilities")
        );
        
        Button viewInventoryBtn = new Button("View Current Inventory");
        viewInventoryBtn.setOnAction(e -> statusLabel.setText("Inventory data available through POS system"));
        
        content.getChildren().addAll(title, new Separator(), info, viewInventoryBtn);
        tab.setContent(new ScrollPane(content));
        
        mainTabPane.getSelectionModel().select(tab);
    }

    private void showCustomersTab() {
        Tab tab = findOrCreateTab("Customer Management", "customer-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Customer Management");
        title.setFont(Font.font("System", FontWeight.BOLD, 24));
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Customer Loyalty Program:"),
            new Label("• 4-tier membership system (Bronze/Silver/Gold/Platinum)"),
            new Label("• Manual loyalty point management"),
            new Label("• Purchase history tracking"),
            new Label("• Suggested discounts (manual application only)"),
            new Label("• Customer search and selection")
        );
        
        Button viewCustomersBtn = new Button("Access Customer Database");
        viewCustomersBtn.setOnAction(e -> statusLabel.setText("Customer management available through POS system"));
        
        content.getChildren().addAll(title, new Separator(), info, viewCustomersBtn);
        tab.setContent(new ScrollPane(content));
        
        mainTabPane.getSelectionModel().select(tab);
    }

    private void showReportsTab() {
        Tab tab = findOrCreateTab("Reports & Analytics", "reports-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Reports & Analytics");
        title.setFont(Font.font("System", FontWeight.BOLD, 24));
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Available Reports:"),
            new Label("• Customer ranking by spending"),
            new Label("• Inventory valuation reports"),
            new Label("• Low stock monitoring"),
            new Label("• Transaction history"),
            new Label("• Membership distribution analytics"),
            new Label("• Manual discount usage tracking")
        );
        
        Button runTestsBtn = new Button("Run System Tests");
        runTestsBtn.setOnAction(e -> {
            new Thread(() -> ComprehensiveTestSuite.main(new String[]{})).start();
            statusLabel.setText("System tests running...");
        });
        
        content.getChildren().addAll(title, new Separator(), info, runTestsBtn);
        tab.setContent(new ScrollPane(content));
        
        mainTabPane.getSelectionModel().select(tab);
    }

    private void showAbout() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About");
        alert.setHeaderText("Clothing Store POS & Inventory Management System");
        alert.setContentText(
            "Version 1.0\n\n" +
            "Features:\n" +
            "• Manual Discount Control System\n" +
            "• Real-time Inventory Management\n" +
            "• Customer Loyalty Program\n" +
            "• Professional Point of Sale Interface\n" +
            "• Comprehensive Reporting\n\n" +
            "Built with Java and JavaFX\n" +
            "Database: SQLite\n" +
            "Architecture: Model-View-Controller (MVC)"
        );
        alert.showAndWait();
    }

    private Tab findOrCreateTab(String title, String id) {
        for (Tab tab : mainTabPane.getTabs()) {
            if (id.equals(tab.getId())) {
                return tab;
            }
        }
        
        Tab newTab = new Tab(title);
        newTab.setId(id);
        newTab.setClosable(true);
        mainTabPane.getTabs().add(newTab);
        return newTab;
    }

    @Override
    public void stop() {
        try {
            if (clockTimer != null) {
                clockTimer.cancel();
            }
            DatabaseManager.getInstance().closeConnection();
        } catch (Exception e) {
            System.err.println("Error during application shutdown: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}

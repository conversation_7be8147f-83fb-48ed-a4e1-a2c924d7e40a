/* Main Application Styles */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 12px;
    -fx-background-color: #f5f5f5;
}

/* Menu Bar Styles */
.main-menu-bar {
    -fx-background-color: #2c3e50;
}

.main-menu-bar .menu-button {
    -fx-text-fill: white;
    -fx-background-color: transparent;
}

.main-menu-bar .menu-button:hover {
    -fx-background-color: #34495e;
}

.main-menu-bar .menu-item {
    -fx-background-color: white;
}

.main-menu-bar .menu-item:focused {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* Toolbar Styles */
.main-toolbar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

.toolbar-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.toolbar-button:hover {
    -fx-background-color: #2980b9;
}

.toolbar-button:pressed {
    -fx-background-color: #21618c;
}

.user-label, .time-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

/* Navigation Panel Styles */
.navigation-panel {
    -fx-background-color: #34495e;
    -fx-border-color: #2c3e50;
    -fx-border-width: 0 1 0 0;
}

.nav-header {
    -fx-text-fill: #ecf0f1;
    -fx-font-weight: bold;
    -fx-padding: 0 0 10 0;
}

.nav-buttons {
    -fx-spacing: 5;
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #ecf0f1;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 12 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 13px;
}

.nav-button:hover {
    -fx-background-color: #2c3e50;
}

.nav-button:pressed {
    -fx-background-color: #1a252f;
}

.nav-button.selected {
    -fx-background-color: #3498db;
}

/* Content Area Styles */
.content-area {
    -fx-background-color: white;
    -fx-padding: 20;
}

/* Welcome Panel Styles */
.welcome-panel {
    -fx-background-color: white;
    -fx-padding: 40;
}

.welcome-title {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

/* Quick Action Cards */
.quick-action-card {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 30;
    -fx-min-width: 200;
    -fx-min-height: 150;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.quick-action-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);
    -fx-cursor: hand;
}

.card-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.card-description {
    -fx-font-size: 12px;
    -fx-text-fill: #7f8c8d;
}

.card-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 20;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.card-button:hover {
    -fx-background-color: #2980b9;
}

/* Status Bar Styles */
.status-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1 0 0 0;
    -fx-font-size: 11px;
}

/* Table Styles */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
}

.table-view .column-header {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

.table-view .column-header:hover {
    -fx-background-color: #2c3e50;
}

.table-row-cell {
    -fx-background-color: white;
}

.table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.table-row-cell:hover {
    -fx-background-color: #e8f4fd;
}

/* Form Styles */
.form-container {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-spacing: 15;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
}

.form-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.form-label {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.text-field, .text-area, .combo-box, .date-picker {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-padding: 8;
}

.text-field:focused, .text-area:focused, .combo-box:focused, .date-picker:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

/* Button Styles */
.button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.button:hover {
    -fx-background-color: #2980b9;
}

.button:pressed {
    -fx-background-color: #21618c;
}

.button.success {
    -fx-background-color: #27ae60;
}

.button.success:hover {
    -fx-background-color: #229954;
}

.button.warning {
    -fx-background-color: #f39c12;
}

.button.warning:hover {
    -fx-background-color: #e67e22;
}

.button.danger {
    -fx-background-color: #e74c3c;
}

.button.danger:hover {
    -fx-background-color: #c0392b;
}

.button.secondary {
    -fx-background-color: #95a5a6;
}

.button.secondary:hover {
    -fx-background-color: #7f8c8d;
}

/* Alert and Dialog Styles */
.alert {
    -fx-background-color: white;
}

.alert .header-panel {
    -fx-background-color: #34495e;
}

.alert .header-panel .label {
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

/* Progress Indicator */
.progress-indicator {
    -fx-progress-color: #3498db;
}

.progress-bar {
    -fx-accent: #3498db;
}

/* Scroll Pane */
.scroll-pane {
    -fx-background-color: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

/* Tab Pane */
.tab-pane {
    -fx-tab-min-width: 100;
}

.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #ecf0f1;
}

.tab-pane .tab {
    -fx-background-color: #bdc3c7;
    -fx-text-fill: #2c3e50;
}

.tab-pane .tab:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.tab-pane .tab:hover {
    -fx-background-color: #95a5a6;
}

/* Separator */
.separator {
    -fx-background-color: #bdc3c7;
}

/* Tooltip */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
}

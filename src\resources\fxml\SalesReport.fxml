<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SalesReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Sales Reports & Analytics">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExportAll" onAction="#handleExportAll" styleClass="button success" text="📊 Export All" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Date Range Selection -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-label" text="Date Range:" />
            <DatePicker fx:id="dpStartDate" promptText="Start Date" />
            <Label styleClass="form-label" text="to" />
            <DatePicker fx:id="dpEndDate" promptText="End Date" />
            <Button fx:id="btnApplyDateRange" onAction="#handleApplyDateRange" styleClass="button" text="Apply" />
            <Separator orientation="VERTICAL" />
            <Button fx:id="btnToday" onAction="#handleToday" styleClass="button secondary" text="Today" />
            <Button fx:id="btnThisWeek" onAction="#handleThisWeek" styleClass="button secondary" text="This Week" />
            <Button fx:id="btnThisMonth" onAction="#handleThisMonth" styleClass="button secondary" text="This Month" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Key Metrics Dashboard -->
      <GridPane hgap="20.0" vgap="20.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
            <children>
               <Label styleClass="title" text="Total Sales" />
               <Label fx:id="lblTotalSales" styleClass="value" text="$0.00" />
               <Label fx:id="lblSalesChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
            <children>
               <Label styleClass="title" text="Transactions" />
               <Label fx:id="lblTotalTransactions" styleClass="value" text="0" />
               <Label fx:id="lblTransactionChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
            <children>
               <Label styleClass="title" text="Items Sold" />
               <Label fx:id="lblItemsSold" styleClass="value" text="0" />
               <Label fx:id="lblItemsChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="3" GridPane.rowIndex="0">
            <children>
               <Label styleClass="title" text="Avg Transaction" />
               <Label fx:id="lblAvgTransaction" styleClass="value" text="$0.00" />
               <Label fx:id="lblAvgChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="0" GridPane.rowIndex="1">
            <children>
               <Label styleClass="title" text="Total Discount" />
               <Label fx:id="lblTotalDiscount" styleClass="value" text="$0.00" />
               <Label fx:id="lblDiscountPercent" styleClass="subtitle" text="0% of sales" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
            <children>
               <Label styleClass="title" text="Unique Customers" />
               <Label fx:id="lblUniqueCustomers" styleClass="value" text="0" />
               <Label fx:id="lblCustomerChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
            <children>
               <Label styleClass="title" text="Return Rate" />
               <Label fx:id="lblReturnRate" styleClass="value" text="0%" />
               <Label fx:id="lblReturnChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox alignment="CENTER" spacing="5.0" styleClass="dashboard-card" GridPane.columnIndex="3" GridPane.rowIndex="1">
            <children>
               <Label styleClass="title" text="Profit Margin" />
               <Label fx:id="lblProfitMargin" styleClass="value" text="0%" />
               <Label fx:id="lblProfitChange" styleClass="subtitle" text="vs previous period" />
            </children>
         </VBox>
         
         <VBox.margin>
            <Insets bottom="20.0" />
         </VBox.margin>
      </GridPane>

      <!-- Report Tabs -->
      <TabPane fx:id="tabReports" VBox.vgrow="ALWAYS">
         <tabs>
            <!-- Sales by Category Tab -->
            <Tab text="Sales by Category">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="form-title" text="Sales Performance by Category" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnExportCategory" onAction="#handleExportCategory" text="📊 Export" />
                           </children>
                        </HBox>
                        <TableView fx:id="tblCategorySales" styleClass="data-grid">
                           <columns>
                              <TableColumn fx:id="colCategoryName" prefWidth="150.0" text="Category" />
                              <TableColumn fx:id="colCategoryItems" prefWidth="100.0" text="Items Sold" />
                              <TableColumn fx:id="colCategorySales" prefWidth="120.0" text="Total Sales" />
                              <TableColumn fx:id="colCategoryPercent" prefWidth="100.0" text="% of Total" />
                              <TableColumn fx:id="colCategoryAvg" prefWidth="120.0" text="Avg Item Price" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Top Products Tab -->
            <Tab text="Top Products">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="form-title" text="Best Selling Products" />
                              <Region HBox.hgrow="ALWAYS" />
                              <ComboBox fx:id="cmbTopProductsLimit" onAction="#handleTopProductsLimit" prefWidth="100.0" />
                              <Button fx:id="btnExportProducts" onAction="#handleExportProducts" text="📊 Export" />
                           </children>
                        </HBox>
                        <TableView fx:id="tblTopProducts" styleClass="data-grid">
                           <columns>
                              <TableColumn fx:id="colProductRank" prefWidth="50.0" text="Rank" />
                              <TableColumn fx:id="colProductSku" prefWidth="80.0" text="SKU" />
                              <TableColumn fx:id="colProductName" prefWidth="200.0" text="Product Name" />
                              <TableColumn fx:id="colProductQuantity" prefWidth="80.0" text="Qty Sold" />
                              <TableColumn fx:id="colProductRevenue" prefWidth="100.0" text="Revenue" />
                              <TableColumn fx:id="colProductProfit" prefWidth="100.0" text="Profit" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Customer Analysis Tab -->
            <Tab text="Customer Analysis">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="form-title" text="Customer Purchase Analysis" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnExportCustomers" onAction="#handleExportCustomers" text="📊 Export" />
                           </children>
                        </HBox>
                        <TableView fx:id="tblCustomerAnalysis" styleClass="data-grid">
                           <columns>
                              <TableColumn fx:id="colCustomerName" prefWidth="150.0" text="Customer" />
                              <TableColumn fx:id="colCustomerTransactions" prefWidth="100.0" text="Transactions" />
                              <TableColumn fx:id="colCustomerItems" prefWidth="100.0" text="Items Bought" />
                              <TableColumn fx:id="colCustomerSpent" prefWidth="120.0" text="Total Spent" />
                              <TableColumn fx:id="colCustomerAvgOrder" prefWidth="120.0" text="Avg Order" />
                              <TableColumn fx:id="colCustomerLastVisit" prefWidth="120.0" text="Last Visit" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Daily Trends Tab -->
            <Tab text="Daily Trends">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="form-title" text="Daily Sales Trends" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnExportTrends" onAction="#handleExportTrends" text="📊 Export" />
                           </children>
                        </HBox>
                        <TableView fx:id="tblDailyTrends" styleClass="data-grid">
                           <columns>
                              <TableColumn fx:id="colTrendDate" prefWidth="100.0" text="Date" />
                              <TableColumn fx:id="colTrendTransactions" prefWidth="100.0" text="Transactions" />
                              <TableColumn fx:id="colTrendItems" prefWidth="100.0" text="Items Sold" />
                              <TableColumn fx:id="colTrendSales" prefWidth="120.0" text="Total Sales" />
                              <TableColumn fx:id="colTrendAvgOrder" prefWidth="120.0" text="Avg Order" />
                              <TableColumn fx:id="colTrendCustomers" prefWidth="100.0" text="Customers" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>

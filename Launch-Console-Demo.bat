@echo off
title Clothing Store POS System - Console Demo
echo ========================================
echo  CLOTHING STORE POS SYSTEM - CONSOLE
echo ========================================
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "bin\com\clothingstore\demo\SimplePOSDemo.class" (
    echo Compiling application...
    javac -cp "lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java src/com/clothingstore/demo/SimplePOSDemo.java
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo Compilation successful!
    echo.
)

echo Running POS System Demo...
echo.
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.demo.SimplePOSDemo

echo.
echo Demo completed.
pause

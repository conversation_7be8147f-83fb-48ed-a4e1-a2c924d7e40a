@echo off
title Clothing Store POS System - Test Suite
echo ========================================
echo  CLOTHING STORE POS SYSTEM - TESTS
echo ========================================
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

echo Compiling test suite...
javac -cp "lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java src/com/clothingstore/util/ValidationUtil.java src/com/clothingstore/util/FormatUtil.java src/com/clothingstore/test/ComprehensiveTestSuite.java

if errorlevel 1 (
    echo ERROR: Compilation failed
    pause
    exit /b 1
)

echo.
echo Running comprehensive test suite...
echo.
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.test.ComprehensiveTestSuite

echo.
echo Running transaction service test...
echo.
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.test.TransactionServiceTest

echo.
echo All tests completed.
pause

package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Product;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Data Access Object for Product operations
 */
public class ProductDAO {
    
    private static ProductDAO instance;
    
    private ProductDAO() {}
    
    public static synchronized ProductDAO getInstance() {
        if (instance == null) {
            instance = new ProductDAO();
        }
        return instance;
    }
    
    public List<Product> findAll() throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        }
        
        return products;
    }
    
    public Optional<Product> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM products WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToProduct(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    public Optional<Product> findBySku(String sku) throws SQLException {
        String sql = "SELECT * FROM products WHERE sku = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, sku);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToProduct(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    public List<Product> findByCategory(String category) throws SQLException {
        String sql = "SELECT * FROM products WHERE category = ? AND active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, category);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }
        
        return products;
    }
    
    public List<Product> searchProducts(String searchTerm) throws SQLException {
        String sql = """
            SELECT * FROM products 
            WHERE active = 1 AND (
                name LIKE ? OR 
                description LIKE ? OR 
                sku LIKE ? OR 
                category LIKE ? OR 
                brand LIKE ?
            ) ORDER BY name
        """;
        
        List<Product> products = new ArrayList<>();
        String searchPattern = "%" + searchTerm + "%";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 1; i <= 5; i++) {
                pstmt.setString(i, searchPattern);
            }
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }
        
        return products;
    }
    
    public List<Product> findLowStockProducts() throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 AND stock_quantity <= min_stock_level ORDER BY stock_quantity";
        List<Product> products = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        }
        
        return products;
    }
    
    public Product save(Product product) throws SQLException {
        if (product.getId() == null) {
            return insert(product);
        } else {
            return update(product);
        }
    }
    
    private Product insert(Product product) throws SQLException {
        String sql = """
            INSERT INTO products (sku, name, description, category, brand, color, size, 
                                price, cost_price, stock_quantity, min_stock_level, image_url, active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            setPreparedStatementParameters(pstmt, product);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating product failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    product.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating product failed, no ID obtained.");
                }
            }
        }
        
        return product;
    }
    
    private Product update(Product product) throws SQLException {
        String sql = """
            UPDATE products SET sku = ?, name = ?, description = ?, category = ?, brand = ?, 
                              color = ?, size = ?, price = ?, cost_price = ?, stock_quantity = ?, 
                              min_stock_level = ?, image_url = ?, active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            setPreparedStatementParameters(pstmt, product);
            pstmt.setLong(14, product.getId());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating product failed, no rows affected.");
            }
        }
        
        return product;
    }
    
    public void delete(Long id) throws SQLException {
        // Soft delete - mark as inactive
        String sql = "UPDATE products SET active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Deleting product failed, no rows affected.");
            }
        }
    }
    
    public void updateStock(Long productId, int newQuantity) throws SQLException {
        String sql = "UPDATE products SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, newQuantity);
            pstmt.setLong(2, productId);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating stock failed, no rows affected.");
            }
        }
    }
    
    public List<String> getAllCategories() throws SQLException {
        String sql = "SELECT DISTINCT category FROM products WHERE active = 1 ORDER BY category";
        List<String> categories = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                categories.add(rs.getString("category"));
            }
        }
        
        return categories;
    }
    
    public List<String> getAllBrands() throws SQLException {
        String sql = "SELECT DISTINCT brand FROM products WHERE active = 1 AND brand IS NOT NULL ORDER BY brand";
        List<String> brands = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                brands.add(rs.getString("brand"));
            }
        }
        
        return brands;
    }
    
    private void setPreparedStatementParameters(PreparedStatement pstmt, Product product) throws SQLException {
        pstmt.setString(1, product.getSku());
        pstmt.setString(2, product.getName());
        pstmt.setString(3, product.getDescription());
        pstmt.setString(4, product.getCategory());
        pstmt.setString(5, product.getBrand());
        pstmt.setString(6, product.getColor());
        pstmt.setString(7, product.getSize());
        pstmt.setBigDecimal(8, product.getPrice());
        pstmt.setBigDecimal(9, product.getCostPrice());
        pstmt.setInt(10, product.getStockQuantity());
        pstmt.setInt(11, product.getMinStockLevel());
        pstmt.setString(12, product.getImageUrl());
        pstmt.setBoolean(13, product.isActive());
    }
    
    private Product mapResultSetToProduct(ResultSet rs) throws SQLException {
        Product product = new Product();
        product.setId(rs.getLong("id"));
        product.setSku(rs.getString("sku"));
        product.setName(rs.getString("name"));
        product.setDescription(rs.getString("description"));
        product.setCategory(rs.getString("category"));
        product.setBrand(rs.getString("brand"));
        product.setColor(rs.getString("color"));
        product.setSize(rs.getString("size"));
        product.setPrice(rs.getBigDecimal("price"));
        product.setCostPrice(rs.getBigDecimal("cost_price"));
        product.setStockQuantity(rs.getInt("stock_quantity"));
        product.setMinStockLevel(rs.getInt("min_stock_level"));
        product.setImageUrl(rs.getString("image_url"));
        product.setActive(rs.getBoolean("active"));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            product.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            product.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return product;
    }
}

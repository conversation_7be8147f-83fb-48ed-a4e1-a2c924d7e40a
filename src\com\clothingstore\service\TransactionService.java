package com.clothingstore.service;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service class for handling transaction business logic
 */
public class TransactionService {
    
    private static TransactionService instance;
    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;
    
    private TransactionService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
    }
    
    public static synchronized TransactionService getInstance() {
        if (instance == null) {
            instance = new TransactionService();
        }
        return instance;
    }
    
    /**
     * Process a complete transaction including inventory updates and customer loyalty points
     */
    public Transaction processTransaction(Transaction transaction) throws SQLException, InsufficientStockException {
        // Validate stock availability
        validateStockAvailability(transaction);
        
        // Generate transaction number if not set
        if (transaction.getTransactionNumber() == null) {
            transaction.setTransactionNumber(transactionDAO.generateTransactionNumber());
        }
        
        // Set transaction date if not set
        if (transaction.getTransactionDate() == null) {
            transaction.setTransactionDate(LocalDateTime.now());
        }
        
        // Recalculate amounts
        transaction.recalculateAmounts();
        
        // Save transaction
        Transaction savedTransaction = transactionDAO.save(transaction);
        
        // Update product stock
        updateProductStock(savedTransaction);
        
        // Update customer purchase history and loyalty points
        if (savedTransaction.getCustomerId() != null) {
            updateCustomerPurchaseHistory(savedTransaction);
        }
        
        return savedTransaction;
    }
    
    /**
     * Process a refund for a transaction
     */
    public Transaction processRefund(Long transactionId, String reason) throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }
        
        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded()) {
            throw new InvalidRefundException("Transaction cannot be refunded");
        }
        
        // Restore product stock
        restoreProductStock(transaction);
        
        // Update customer purchase history (subtract amounts)
        if (transaction.getCustomerId() != null) {
            reverseCustomerPurchaseHistory(transaction);
        }
        
        // Mark transaction as refunded
        transaction.processRefund();
        transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "") + 
                           "Refunded: " + reason);
        
        return transactionDAO.save(transaction);
    }
    
    /**
     * Process a partial refund for specific items
     */
    public Transaction processPartialRefund(Long transactionId, List<TransactionItem> refundItems, String reason) 
            throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }
        
        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded() && !"PARTIALLY_REFUNDED".equals(transaction.getStatus())) {
            throw new InvalidRefundException("Transaction cannot be refunded");
        }
        
        BigDecimal refundAmount = BigDecimal.ZERO;
        
        // Process each refund item
        for (TransactionItem refundItem : refundItems) {
            // Find the original item
            TransactionItem originalItem = transaction.getItems().stream()
                .filter(item -> item.getProductId().equals(refundItem.getProductId()))
                .findFirst()
                .orElseThrow(() -> new InvalidRefundException("Item not found in transaction"));
            
            // Validate refund quantity
            if (refundItem.getQuantity() > originalItem.getQuantity()) {
                throw new InvalidRefundException("Refund quantity exceeds original quantity");
            }
            
            // Restore stock for refunded items
            Optional<Product> product = productDAO.findById(refundItem.getProductId());
            if (product.isPresent()) {
                Product p = product.get();
                p.addStock(refundItem.getQuantity());
                productDAO.save(p);
            }
            
            // Calculate refund amount
            BigDecimal itemRefundAmount = refundItem.getUnitPrice()
                .multiply(BigDecimal.valueOf(refundItem.getQuantity()));
            refundAmount = refundAmount.add(itemRefundAmount);
        }
        
        // Update transaction status
        transaction.processPartialRefund(refundAmount);
        transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "") + 
                           "Partial refund: " + reason + " (Amount: $" + refundAmount + ")");
        
        return transactionDAO.save(transaction);
    }
    
    /**
     * Get transaction history for a customer
     */
    public List<Transaction> getCustomerTransactionHistory(Long customerId) throws SQLException {
        return transactionDAO.findByCustomerId(customerId);
    }
    
    /**
     * Get transactions within a date range
     */
    public List<Transaction> getTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        return transactionDAO.findByDateRange(startDate, endDate);
    }
    
    /**
     * Get daily sales summary
     */
    public SalesSummary getDailySales(LocalDateTime date) throws SQLException {
        LocalDateTime startOfDay = date.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = date.toLocalDate().atTime(23, 59, 59);
        
        List<Transaction> transactions = transactionDAO.findByDateRange(startOfDay, endOfDay);
        return calculateSalesSummary(transactions);
    }
    
    /**
     * Calculate sales summary from transactions
     */
    private SalesSummary calculateSalesSummary(List<Transaction> transactions) {
        SalesSummary summary = new SalesSummary();
        
        for (Transaction transaction : transactions) {
            if ("COMPLETED".equals(transaction.getStatus())) {
                summary.addTransaction(transaction);
            }
        }
        
        return summary;
    }
    
    private void validateStockAvailability(Transaction transaction) throws InsufficientStockException, SQLException {
        for (TransactionItem item : transaction.getItems()) {
            Optional<Product> product = productDAO.findById(item.getProductId());
            if (product.isEmpty()) {
                throw new InsufficientStockException("Product not found: " + item.getProductId());
            }
            
            Product p = product.get();
            if (p.getStockQuantity() < item.getQuantity()) {
                throw new InsufficientStockException(
                    "Insufficient stock for " + p.getName() + 
                    ". Available: " + p.getStockQuantity() + 
                    ", Required: " + item.getQuantity());
            }
        }
    }
    
    private void updateProductStock(Transaction transaction) throws SQLException {
        for (TransactionItem item : transaction.getItems()) {
            Optional<Product> product = productDAO.findById(item.getProductId());
            if (product.isPresent()) {
                Product p = product.get();
                p.reduceStock(item.getQuantity());
                productDAO.save(p);
            }
        }
    }
    
    private void restoreProductStock(Transaction transaction) throws SQLException {
        for (TransactionItem item : transaction.getItems()) {
            Optional<Product> product = productDAO.findById(item.getProductId());
            if (product.isPresent()) {
                Product p = product.get();
                p.addStock(item.getQuantity());
                productDAO.save(p);
            }
        }
    }
    
    private void updateCustomerPurchaseHistory(Transaction transaction) throws SQLException {
        customerDAO.updatePurchaseHistory(transaction.getCustomerId(), 
                                        transaction.getTotalAmount().doubleValue());
    }
    
    private void reverseCustomerPurchaseHistory(Transaction transaction) throws SQLException {
        // This would require more complex logic to properly reverse loyalty points and spending
        // For now, we'll just add a note that manual adjustment may be needed
        Optional<Customer> customer = customerDAO.findById(transaction.getCustomerId());
        if (customer.isPresent()) {
            Customer c = customer.get();
            c.setTotalSpent(c.getTotalSpent() - transaction.getTotalAmount().doubleValue());
            c.setTotalPurchases(c.getTotalPurchases() - 1);
            // Note: Loyalty points reversal would need more sophisticated logic
            customerDAO.save(c);
        }
    }
    
    /**
     * Custom exception for insufficient stock
     */
    public static class InsufficientStockException extends Exception {
        public InsufficientStockException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for invalid refunds
     */
    public static class InvalidRefundException extends Exception {
        public InvalidRefundException(String message) {
            super(message);
        }
    }
    
    /**
     * Sales summary data class
     */
    public static class SalesSummary {
        private int transactionCount = 0;
        private int itemCount = 0;
        private BigDecimal totalSales = BigDecimal.ZERO;
        private BigDecimal totalTax = BigDecimal.ZERO;
        private BigDecimal totalDiscount = BigDecimal.ZERO;
        
        public void addTransaction(Transaction transaction) {
            transactionCount++;
            itemCount += transaction.getTotalItemCount();
            totalSales = totalSales.add(transaction.getTotalAmount());
            totalTax = totalTax.add(transaction.getTaxAmount());
            totalDiscount = totalDiscount.add(transaction.getDiscountAmount());
        }
        
        // Getters
        public int getTransactionCount() { return transactionCount; }
        public int getItemCount() { return itemCount; }
        public BigDecimal getTotalSales() { return totalSales; }
        public BigDecimal getTotalTax() { return totalTax; }
        public BigDecimal getTotalDiscount() { return totalDiscount; }
        public BigDecimal getAverageTransactionValue() {
            return transactionCount > 0 ? totalSales.divide(BigDecimal.valueOf(transactionCount), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        }
    }
}

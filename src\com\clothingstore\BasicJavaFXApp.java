package com.clothingstore;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.gui.SwingPOSDemo;
import com.clothingstore.demo.SimplePOSDemo;
import com.clothingstore.demo.ManualDiscountDemo;

/**
 * Basic JavaFX Application for Clothing Store POS System
 * No external dependencies, self-contained styling
 */
public class BasicJavaFXApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database
            DatabaseManager.getInstance().initializeDatabase();

            // Create main layout
            VBox root = createMainLayout();
            
            // Create scene with inline styling
            Scene scene = new Scene(root, 1000, 700);
            
            // Apply inline CSS styling
            scene.getRoot().setStyle(
                "-fx-font-family: 'Segoe UI', Arial, sans-serif; " +
                "-fx-font-size: 12px; " +
                "-fx-background-color: #f5f5f5;"
            );

            // Configure stage
            primaryStage.setTitle("Clothing Store POS & Inventory Management System");
            primaryStage.setScene(scene);
            primaryStage.setResizable(true);
            primaryStage.setOnCloseRequest(e -> Platform.exit());

            // Show application
            primaryStage.show();

        } catch (Exception e) {
            // Simple error handling without external dependencies
            System.err.println("Application startup error: " + e.getMessage());
            e.printStackTrace();
            
            // Show simple alert
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Startup Error");
            alert.setHeaderText("Application Failed to Start");
            alert.setContentText("Error: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private VBox createMainLayout() {
        VBox root = new VBox(20);
        root.setPadding(new Insets(30));
        root.setAlignment(Pos.CENTER);
        
        // Title section
        VBox titleSection = createTitleSection();
        
        // Main buttons
        VBox buttonSection = createButtonSection();
        
        // Features section
        VBox featuresSection = createFeaturesSection();
        
        // Status section
        VBox statusSection = createStatusSection();
        
        root.getChildren().addAll(titleSection, buttonSection, featuresSection, statusSection);
        
        return root;
    }

    private VBox createTitleSection() {
        VBox titleBox = new VBox(10);
        titleBox.setAlignment(Pos.CENTER);
        
        Label mainTitle = new Label("Clothing Store POS & Inventory Management System");
        mainTitle.setFont(Font.font("System", FontWeight.BOLD, 24));
        mainTitle.setStyle("-fx-text-fill: #2c3e50;");
        
        Label subtitle = new Label("Manual Discount Control System");
        subtitle.setFont(Font.font("System", FontWeight.NORMAL, 16));
        subtitle.setStyle("-fx-text-fill: #7f8c8d;");
        
        titleBox.getChildren().addAll(mainTitle, subtitle);
        
        return titleBox;
    }

    private VBox createButtonSection() {
        VBox buttonBox = new VBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20));
        
        // Create styled buttons
        Button swingPOSBtn = createStyledButton("Launch Professional Swing POS", "#27ae60");
        swingPOSBtn.setOnAction(e -> {
            new Thread(() -> SwingPOSDemo.main(new String[]{})).start();
            showStatus("Swing POS Interface Launched Successfully!");
        });
        
        Button consoleDemoBtn = createStyledButton("Run Console Demo", "#3498db");
        consoleDemoBtn.setOnAction(e -> {
            new Thread(() -> SimplePOSDemo.main(new String[]{})).start();
            showStatus("Console Demo Started");
        });
        
        Button discountDemoBtn = createStyledButton("Manual Discount Demo", "#e74c3c");
        discountDemoBtn.setOnAction(e -> {
            new Thread(() -> ManualDiscountDemo.main(new String[]{})).start();
            showStatus("Manual Discount Demo Started");
        });
        
        Button aboutBtn = createStyledButton("About System", "#9b59b6");
        aboutBtn.setOnAction(e -> showAbout());
        
        Button exitBtn = createStyledButton("Exit Application", "#95a5a6");
        exitBtn.setOnAction(e -> Platform.exit());
        
        buttonBox.getChildren().addAll(swingPOSBtn, consoleDemoBtn, discountDemoBtn, aboutBtn, exitBtn);
        
        return buttonBox;
    }

    private Button createStyledButton(String text, String color) {
        Button button = new Button(text);
        button.setPrefWidth(300);
        button.setPrefHeight(50);
        button.setFont(Font.font("System", FontWeight.BOLD, 14));
        button.setStyle(
            "-fx-background-color: " + color + "; " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 5; " +
            "-fx-border-radius: 5; " +
            "-fx-cursor: hand;"
        );
        
        // Add hover effect
        button.setOnMouseEntered(e -> {
            button.setStyle(
                "-fx-background-color: derive(" + color + ", -10%); " +
                "-fx-text-fill: white; " +
                "-fx-background-radius: 5; " +
                "-fx-border-radius: 5; " +
                "-fx-cursor: hand;"
            );
        });
        
        button.setOnMouseExited(e -> {
            button.setStyle(
                "-fx-background-color: " + color + "; " +
                "-fx-text-fill: white; " +
                "-fx-background-radius: 5; " +
                "-fx-border-radius: 5; " +
                "-fx-cursor: hand;"
            );
        });
        
        return button;
    }

    private VBox createFeaturesSection() {
        VBox featuresBox = new VBox(10);
        featuresBox.setAlignment(Pos.CENTER);
        featuresBox.setPadding(new Insets(20));
        featuresBox.setStyle(
            "-fx-background-color: white; " +
            "-fx-border-color: #bdc3c7; " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 5; " +
            "-fx-background-radius: 5;"
        );
        
        Label featuresTitle = new Label("System Features");
        featuresTitle.setFont(Font.font("System", FontWeight.BOLD, 16));
        featuresTitle.setStyle("-fx-text-fill: #2c3e50;");
        
        VBox featuresList = new VBox(5);
        featuresList.setAlignment(Pos.CENTER_LEFT);
        
        String[] features = {
            "✓ Manual Discount Controls - No Automatic Discounts",
            "✓ Professional Swing GUI Interface",
            "✓ Real-time Inventory Tracking",
            "✓ Customer Loyalty Program Management",
            "✓ Comprehensive Transaction Processing",
            "✓ SQLite Database Integration",
            "✓ Multiple Interface Options"
        };
        
        for (String feature : features) {
            Label featureLabel = new Label(feature);
            featureLabel.setStyle("-fx-text-fill: #34495e;");
            featuresList.getChildren().add(featureLabel);
        }
        
        featuresBox.getChildren().addAll(featuresTitle, featuresList);
        
        return featuresBox;
    }

    private VBox createStatusSection() {
        VBox statusBox = new VBox(10);
        statusBox.setAlignment(Pos.CENTER);
        statusBox.setPadding(new Insets(10));
        statusBox.setStyle(
            "-fx-background-color: #ecf0f1; " +
            "-fx-border-color: #bdc3c7; " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 3; " +
            "-fx-background-radius: 3;"
        );
        
        Label statusLabel = new Label("System Status: Ready - Manual Discount Controls Active");
        statusLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
        
        Label instructionLabel = new Label("Click any button above to launch the corresponding application");
        instructionLabel.setStyle("-fx-text-fill: #7f8c8d;");
        
        statusBox.getChildren().addAll(statusLabel, instructionLabel);
        
        return statusBox;
    }

    private void showStatus(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("System Status");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showAbout() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About Clothing Store POS System");
        alert.setHeaderText("Clothing Store POS & Inventory Management System");
        alert.setContentText(
            "Version 1.0 - Manual Discount Control System\n\n" +
            "Features:\n" +
            "• Manual Discount Controls (No Automatic Discounts)\n" +
            "• Professional Swing GUI Interface\n" +
            "• Real-time Inventory Management\n" +
            "• Customer Loyalty Program\n" +
            "• Comprehensive Transaction Processing\n" +
            "• SQLite Database Integration\n\n" +
            "Technology Stack:\n" +
            "• Java 8+ with JavaFX\n" +
            "• Swing GUI Framework\n" +
            "• SQLite Database\n" +
            "• MVC Architecture Pattern\n\n" +
            "Ready for immediate deployment and commercial use!"
        );
        alert.showAndWait();
    }

    public static void main(String[] args) {
        launch(args);
    }
}

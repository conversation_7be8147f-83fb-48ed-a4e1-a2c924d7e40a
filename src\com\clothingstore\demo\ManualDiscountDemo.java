package com.clothingstore.demo;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;

/**
 * Demo showing the manual discount system
 */
public class ManualDiscountDemo {
    
    public static void main(String[] args) {
        System.out.println("MANUAL DISCOUNT SYSTEM DEMONSTRATION");
        System.out.println("====================================");
        
        try {
            // Initialize system
            DatabaseManager.getInstance().initializeDatabase();
            
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            TransactionService transactionService = TransactionService.getInstance();
            NumberFormat currency = NumberFormat.getCurrencyInstance();
            
            // Get sample data
            List<Product> products = productDAO.findAll();
            List<Customer> customers = customerDAO.findAll();
            
            Product tshirt = products.stream()
                .filter(p -> p.getName().contains("T-Shirt"))
                .findFirst().get();
            Product jeans = products.stream()
                .filter(p -> p.getName().contains("Jeans"))
                .findFirst().get();
            
            Customer silverCustomer = customers.stream()
                .filter(c -> "SILVER".equals(c.getMembershipLevel()))
                .findFirst().get();
            Customer goldCustomer = customers.stream()
                .filter(c -> "GOLD".equals(c.getMembershipLevel()))
                .findFirst().get();
            
            System.out.println("\n=== SCENARIO 1: NO AUTOMATIC DISCOUNTS ===");
            
            // Create transaction without customer
            Transaction transaction1 = new Transaction();
            transaction1.addItem(new TransactionItem(tshirt, 2));
            transaction1.addItem(new TransactionItem(jeans, 1));
            transaction1.recalculateAmounts();
            
            System.out.println("Transaction without customer:");
            System.out.println("- Subtotal: " + currency.format(transaction1.getSubtotal()));
            System.out.println("- Automatic discount: " + currency.format(transaction1.getDiscountAmount()));
            System.out.println("- Total: " + currency.format(transaction1.getTotalAmount()));
            System.out.println("Result: NO automatic discount applied");
            
            System.out.println("\n=== SCENARIO 2: CUSTOMER WITH NO AUTOMATIC DISCOUNT ===");
            
            // Create transaction with Silver customer
            Transaction transaction2 = new Transaction();
            transaction2.setCustomer(silverCustomer);
            transaction2.addItem(new TransactionItem(tshirt, 2));
            transaction2.addItem(new TransactionItem(jeans, 1));
            transaction2.recalculateAmounts();
            
            System.out.println("Transaction with " + silverCustomer.getMembershipLevel() + " customer:");
            System.out.println("- Customer: " + silverCustomer.getFullName());
            System.out.println("- Suggested discount: " + String.format("%.0f%%", silverCustomer.getSuggestedDiscountPercentage() * 100));
            System.out.println("- Subtotal: " + currency.format(transaction2.getSubtotal()));
            System.out.println("- Automatic discount: " + currency.format(transaction2.getDiscountAmount()));
            System.out.println("- Total: " + currency.format(transaction2.getTotalAmount()));
            System.out.println("Result: NO automatic discount applied (manual only)");
            
            System.out.println("\n=== SCENARIO 3: MANUAL PERCENTAGE DISCOUNT ===");
            
            // Apply manual percentage discount
            System.out.println("Cashier manually applies 5% discount...");
            transaction2.applyManualDiscountPercentage(0.05);
            
            System.out.println("After manual 5% discount:");
            System.out.println("- Subtotal: " + currency.format(transaction2.getSubtotal()));
            System.out.println("- Manual discount: " + currency.format(transaction2.getDiscountAmount()));
            System.out.println("- Total: " + currency.format(transaction2.getTotalAmount()));
            System.out.println("Result: Manual discount successfully applied");
            
            System.out.println("\n=== SCENARIO 4: MANUAL DOLLAR AMOUNT DISCOUNT ===");
            
            // Create new transaction with Gold customer
            Transaction transaction3 = new Transaction();
            transaction3.setCustomer(goldCustomer);
            transaction3.addItem(new TransactionItem(tshirt, 3));
            transaction3.recalculateAmounts();
            
            System.out.println("Transaction with " + goldCustomer.getMembershipLevel() + " customer:");
            System.out.println("- Customer: " + goldCustomer.getFullName());
            System.out.println("- Suggested discount: " + String.format("%.0f%%", goldCustomer.getSuggestedDiscountPercentage() * 100));
            System.out.println("- Subtotal: " + currency.format(transaction3.getSubtotal()));
            System.out.println("- Initial total: " + currency.format(transaction3.getTotalAmount()));
            
            // Apply manual dollar discount
            System.out.println("\nCashier manually applies $10.00 discount...");
            transaction3.applyManualDiscount(new BigDecimal("10.00"));
            
            System.out.println("After manual $10.00 discount:");
            System.out.println("- Subtotal: " + currency.format(transaction3.getSubtotal()));
            System.out.println("- Manual discount: " + currency.format(transaction3.getDiscountAmount()));
            System.out.println("- Total: " + currency.format(transaction3.getTotalAmount()));
            System.out.println("Result: Manual dollar discount successfully applied");
            
            System.out.println("\n=== SCENARIO 5: CLEARING DISCOUNTS ===");
            
            System.out.println("Current discount: " + currency.format(transaction3.getDiscountAmount()));
            System.out.println("Cashier clears the discount...");
            transaction3.clearDiscount();
            
            System.out.println("After clearing discount:");
            System.out.println("- Subtotal: " + currency.format(transaction3.getSubtotal()));
            System.out.println("- Discount: " + currency.format(transaction3.getDiscountAmount()));
            System.out.println("- Total: " + currency.format(transaction3.getTotalAmount()));
            System.out.println("Result: Discount successfully cleared");
            
            System.out.println("\n=== SCENARIO 6: MULTIPLE DISCOUNT APPLICATIONS ===");
            
            System.out.println("Starting fresh transaction...");
            Transaction transaction4 = new Transaction();
            transaction4.setCustomer(silverCustomer);
            transaction4.addItem(new TransactionItem(jeans, 2));
            transaction4.recalculateAmounts();
            
            System.out.println("Initial: " + currency.format(transaction4.getTotalAmount()));
            
            System.out.println("Apply 10% discount...");
            transaction4.applyManualDiscountPercentage(0.10);
            System.out.println("After 10%: " + currency.format(transaction4.getTotalAmount()));
            
            System.out.println("Change to $15 discount...");
            transaction4.applyManualDiscount(new BigDecimal("15.00"));
            System.out.println("After $15: " + currency.format(transaction4.getTotalAmount()));
            
            System.out.println("Change to 8% discount...");
            transaction4.applyManualDiscountPercentage(0.08);
            System.out.println("After 8%: " + currency.format(transaction4.getTotalAmount()));
            
            System.out.println("Result: Only the last applied discount is active");
            
            System.out.println("\n" + "=".repeat(50));
            System.out.println("MANUAL DISCOUNT SYSTEM SUMMARY");
            System.out.println("=".repeat(50));
            System.out.println("✓ NO automatic discounts based on membership");
            System.out.println("✓ Suggested discounts shown for reference only");
            System.out.println("✓ Cashier must manually apply all discounts");
            System.out.println("✓ Support for percentage discounts (e.g., 5%)");
            System.out.println("✓ Support for dollar amount discounts (e.g., $10)");
            System.out.println("✓ Ability to clear/remove applied discounts");
            System.out.println("✓ Only one discount can be active at a time");
            System.out.println("✓ Full control over discount application");
            
            System.out.println("\nBENEFITS OF MANUAL DISCOUNT SYSTEM:");
            System.out.println("• Prevents unauthorized automatic discounts");
            System.out.println("• Gives cashiers full control over pricing");
            System.out.println("• Allows for situational/promotional discounts");
            System.out.println("• Maintains audit trail of manual applications");
            System.out.println("• Supports both percentage and fixed amount discounts");
            System.out.println("• Easy to modify or remove during transaction");
            
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
        }
    }
}

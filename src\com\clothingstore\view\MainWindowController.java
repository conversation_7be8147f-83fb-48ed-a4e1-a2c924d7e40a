package com.clothingstore.view;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.layout.StackPane;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.util.AlertUtil;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Main Window Controller for the Clothing Store Management System
 */
public class MainWindowController implements Initializable {

    // Menu items
    @FXML private MenuBar menuBar;
    @FXML private MenuItem menuBackup;
    @FXML private MenuItem menuRestore;
    @FXML private MenuItem menuExit;
    @FXML private MenuItem menuProducts;
    @FXML private MenuItem menuLowStock;
    @FXML private MenuItem menuInventoryReport;
    @FXML private MenuItem menuCustomers;
    @FXML private MenuItem menuCustomerReport;
    @FXML private MenuItem menuPOS;
    @FXML private MenuItem menuTransactions;
    @FXML private MenuItem menuSalesReport;
    @FXML private MenuItem menuDailySales;
    @FXML private MenuItem menuMonthlySales;
    @FXML private MenuItem menuProfitReport;
    @FXML private MenuItem menuAbout;
    @FXML private MenuItem menuHelp;

    // Toolbar
    @FXML private ToolBar toolBar;
    @FXML private Button btnPOS;
    @FXML private Button btnProducts;
    @FXML private Button btnCustomers;
    @FXML private Button btnReports;
    @FXML private Label lblCurrentUser;
    @FXML private Label lblCurrentTime;

    // Navigation
    @FXML private Button navBtnDashboard;
    @FXML private Button navBtnPOS;
    @FXML private Button navBtnProducts;
    @FXML private Button navBtnCustomers;
    @FXML private Button navBtnTransactions;
    @FXML private Button navBtnReports;
    @FXML private Button navBtnSettings;

    // Content area
    @FXML private StackPane contentArea;

    // Status bar
    @FXML private Label lblStatus;
    @FXML private Label lblDatabaseStatus;
    @FXML private Label lblVersion;

    private Timer clockTimer;
    private Button currentSelectedNavButton;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupClock();
        setupNavigation();
        updateDatabaseStatus();
        setStatus("Application started successfully");
        
        // Set default selected navigation button
        selectNavButton(navBtnDashboard);
    }

    private void setupClock() {
        clockTimer = new Timer(true);
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    LocalDateTime now = LocalDateTime.now();
                    lblCurrentTime.setText("Time: " + now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        }, 0, 1000);
    }

    private void setupNavigation() {
        // Add style class to navigation buttons for selection tracking
        Button[] navButtons = {navBtnDashboard, navBtnPOS, navBtnProducts, 
                              navBtnCustomers, navBtnTransactions, navBtnReports, navBtnSettings};
        
        for (Button btn : navButtons) {
            btn.setOnAction(e -> selectNavButton(btn));
        }
    }

    private void selectNavButton(Button selectedButton) {
        // Remove selection from previous button
        if (currentSelectedNavButton != null) {
            currentSelectedNavButton.getStyleClass().remove("selected");
        }
        
        // Add selection to new button
        selectedButton.getStyleClass().add("selected");
        currentSelectedNavButton = selectedButton;
    }

    private void updateDatabaseStatus() {
        try {
            DatabaseManager.getInstance().getConnection();
            lblDatabaseStatus.setText("Database: Connected");
            lblDatabaseStatus.setStyle("-fx-text-fill: green;");
        } catch (Exception e) {
            lblDatabaseStatus.setText("Database: Error");
            lblDatabaseStatus.setStyle("-fx-text-fill: red;");
        }
    }

    private void setStatus(String message) {
        lblStatus.setText(message);
    }

    private void loadContent(String fxmlFile, String title) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/" + fxmlFile));
            Parent content = loader.load();
            contentArea.getChildren().clear();
            contentArea.getChildren().add(content);
            setStatus(title + " loaded");
        } catch (IOException e) {
            AlertUtil.showError("Loading Error", "Failed to load " + title + ": " + e.getMessage());
            setStatus("Error loading " + title);
        }
    }

    // Menu Actions
    @FXML
    private void handleBackup() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Database Backup");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("Database Files", "*.db"));
        
        Stage stage = (Stage) menuBar.getScene().getWindow();
        File file = fileChooser.showSaveDialog(stage);
        
        if (file != null) {
            try {
                DatabaseManager.getInstance().backupDatabase(file.getAbsolutePath());
                AlertUtil.showSuccess("Backup Complete", "Database backed up successfully to: " + file.getName());
                setStatus("Database backup completed");
            } catch (Exception e) {
                AlertUtil.showError("Backup Failed", "Failed to backup database: " + e.getMessage());
                setStatus("Database backup failed");
            }
        }
    }

    @FXML
    private void handleRestore() {
        if (AlertUtil.showConfirmation("Restore Database", 
            "This will replace the current database. Are you sure you want to continue?")) {
            
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Select Database Backup");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Database Files", "*.db"));
            
            Stage stage = (Stage) menuBar.getScene().getWindow();
            File file = fileChooser.showOpenDialog(stage);
            
            if (file != null) {
                try {
                    DatabaseManager.getInstance().restoreDatabase(file.getAbsolutePath());
                    AlertUtil.showSuccess("Restore Complete", "Database restored successfully from: " + file.getName());
                    setStatus("Database restore completed");
                    updateDatabaseStatus();
                } catch (Exception e) {
                    AlertUtil.showError("Restore Failed", "Failed to restore database: " + e.getMessage());
                    setStatus("Database restore failed");
                }
            }
        }
    }

    @FXML
    private void handleExit() {
        if (AlertUtil.showConfirmation("Exit Application", "Are you sure you want to exit?")) {
            if (clockTimer != null) {
                clockTimer.cancel();
            }
            Platform.exit();
        }
    }

    // Navigation Actions
    @FXML
    private void showDashboard() {
        // For now, show the welcome panel (already loaded)
        setStatus("Dashboard view");
    }

    @FXML
    private void showPointOfSale() {
        loadContent("PointOfSale.fxml", "Point of Sale");
        selectNavButton(navBtnPOS);
    }

    @FXML
    private void showProductManagement() {
        loadContent("ProductManagement.fxml", "Product Management");
        selectNavButton(navBtnProducts);
    }

    @FXML
    private void showCustomerManagement() {
        loadContent("CustomerManagement.fxml", "Customer Management");
        selectNavButton(navBtnCustomers);
    }

    @FXML
    private void showTransactionHistory() {
        loadContent("TransactionHistory.fxml", "Transaction History");
        selectNavButton(navBtnTransactions);
    }

    @FXML
    private void showSalesReport() {
        loadContent("SalesReport.fxml", "Sales Report");
        selectNavButton(navBtnReports);
    }

    @FXML
    private void showSettings() {
        loadContent("Settings.fxml", "Settings");
        selectNavButton(navBtnSettings);
    }

    // Report Actions
    @FXML
    private void showLowStockReport() {
        loadContent("LowStockReport.fxml", "Low Stock Report");
    }

    @FXML
    private void showInventoryReport() {
        loadContent("InventoryReport.fxml", "Inventory Report");
    }

    @FXML
    private void showCustomerReport() {
        loadContent("CustomerReport.fxml", "Customer Report");
    }

    @FXML
    private void showDailySalesReport() {
        loadContent("DailySalesReport.fxml", "Daily Sales Report");
    }

    @FXML
    private void showMonthlySalesReport() {
        loadContent("MonthlySalesReport.fxml", "Monthly Sales Report");
    }

    @FXML
    private void showProfitReport() {
        loadContent("ProfitReport.fxml", "Profit Report");
    }

    // Help Actions
    @FXML
    private void showAbout() {
        AlertUtil.showInfo("About", 
            "Clothing Store Management System v1.0.0\n\n" +
            "A comprehensive point-of-sale and inventory management solution\n" +
            "for clothing retail businesses.\n\n" +
            "Built with JavaFX and SQLite\n" +
            "© 2024 Clothing Store Management System");
    }

    @FXML
    private void showHelp() {
        AlertUtil.showInfo("User Guide", 
            "Quick Start Guide:\n\n" +
            "1. Use the navigation panel on the left to access different modules\n" +
            "2. Start with Product Management to add your inventory\n" +
            "3. Add customers in Customer Management\n" +
            "4. Use Point of Sale for transactions\n" +
            "5. View reports for business insights\n\n" +
            "For detailed help, refer to the user manual.");
    }

    public void shutdown() {
        if (clockTimer != null) {
            clockTimer.cancel();
        }
    }
}

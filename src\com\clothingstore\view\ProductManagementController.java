package com.clothingstore.view;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * Controller for Product Management interface
 */
public class ProductManagementController implements Initializable {

    @FXML private TextField txtSearch;
    @FXML private ComboBox<String> cmbCategory;
    @FXML private ComboBox<String> cmbStockStatus;
    @FXML private Button btnAddProduct;
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnExport;
    @FXML private Button btnLowStockReport;

    @FXML private TableView<Product> tblProducts;
    @FXML private TableColumn<Product, String> colSku;
    @FXML private TableColumn<Product, String> colName;
    @FXML private TableColumn<Product, String> colCategory;
    @FXML private TableColumn<Product, String> colBrand;
    @FXML private TableColumn<Product, String> colSize;
    @FXML private TableColumn<Product, String> colColor;
    @FXML private TableColumn<Product, String> colPrice;
    @FXML private TableColumn<Product, Integer> colStock;
    @FXML private TableColumn<Product, Integer> colMinStock;
    @FXML private TableColumn<Product, String> colStatus;
    @FXML private TableColumn<Product, String> colActions;

    @FXML private MenuItem menuEdit;
    @FXML private MenuItem menuDuplicate;
    @FXML private MenuItem menuAdjustStock;
    @FXML private MenuItem menuViewHistory;
    @FXML private MenuItem menuDelete;

    @FXML private Label lblTotalProducts;
    @FXML private Label lblLowStockCount;
    @FXML private Label lblTotalValue;

    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ProductDAO productDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        
        setupTableColumns();
        setupFilters();
        loadProducts();
        updateSummary();
    }

    private void setupTableColumns() {
        colSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colBrand.setCellValueFactory(new PropertyValueFactory<>("brand"));
        colSize.setCellValueFactory(new PropertyValueFactory<>("size"));
        colColor.setCellValueFactory(new PropertyValueFactory<>("color"));
        colStock.setCellValueFactory(new PropertyValueFactory<>("stockQuantity"));
        colMinStock.setCellValueFactory(new PropertyValueFactory<>("minStockLevel"));
        
        // Custom cell factories for formatted display
        colPrice.setCellValueFactory(cellData -> {
            BigDecimal price = cellData.getValue().getPrice();
            return new SimpleStringProperty(price != null ? currencyFormat.format(price) : "");
        });
        
        colStatus.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            String status = product.isLowStock() ? "LOW STOCK" : "OK";
            return new SimpleStringProperty(status);
        });
        
        // Style low stock rows
        tblProducts.setRowFactory(tv -> {
            TableRow<Product> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldProduct, newProduct) -> {
                if (newProduct != null && newProduct.isLowStock()) {
                    row.setStyle("-fx-background-color: #ffebee;");
                } else {
                    row.setStyle("");
                }
            });
            return row;
        });
        
        // Action buttons column
        colActions.setCellFactory(col -> new TableCell<Product, String>() {
            private final Button editBtn = new Button("Edit");
            private final Button stockBtn = new Button("Stock");
            
            {
                editBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    handleEditProduct(product);
                });
                
                stockBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    handleAdjustStock(product);
                });
                
                editBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                stockBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new javafx.scene.layout.HBox(2, editBtn, stockBtn));
                }
            }
        });
        
        tblProducts.setItems(filteredProducts);
    }

    private void setupFilters() {
        // Stock status filter
        cmbStockStatus.setItems(FXCollections.observableArrayList("All", "In Stock", "Low Stock", "Out of Stock"));
        cmbStockStatus.setValue("All");
        
        loadCategories();
    }

    private void loadCategories() {
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            cmbCategory.setItems(categoryItems);
            cmbCategory.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            applyFilters();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void applyFilters() {
        List<Product> filtered = allProducts.stream()
            .filter(this::matchesSearchFilter)
            .filter(this::matchesCategoryFilter)
            .filter(this::matchesStockFilter)
            .collect(Collectors.toList());
        
        filteredProducts.setAll(filtered);
        updateSummary();
    }

    private boolean matchesSearchFilter(Product product) {
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }
        
        String term = searchTerm.toLowerCase();
        return (product.getName() != null && product.getName().toLowerCase().contains(term)) ||
               (product.getSku() != null && product.getSku().toLowerCase().contains(term)) ||
               (product.getDescription() != null && product.getDescription().toLowerCase().contains(term)) ||
               (product.getBrand() != null && product.getBrand().toLowerCase().contains(term));
    }

    private boolean matchesCategoryFilter(Product product) {
        String selectedCategory = cmbCategory.getValue();
        return selectedCategory == null || "All Categories".equals(selectedCategory) ||
               selectedCategory.equals(product.getCategory());
    }

    private boolean matchesStockFilter(Product product) {
        String stockStatus = cmbStockStatus.getValue();
        if (stockStatus == null || "All".equals(stockStatus)) {
            return true;
        }
        
        switch (stockStatus) {
            case "In Stock":
                return product.getStockQuantity() > product.getMinStockLevel();
            case "Low Stock":
                return product.isLowStock() && product.getStockQuantity() > 0;
            case "Out of Stock":
                return product.getStockQuantity() == 0;
            default:
                return true;
        }
    }

    private void updateSummary() {
        int totalProducts = filteredProducts.size();
        long lowStockCount = filteredProducts.stream()
            .mapToLong(p -> p.isLowStock() ? 1 : 0)
            .sum();
        
        BigDecimal totalValue = filteredProducts.stream()
            .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        lblTotalProducts.setText("Total Products: " + totalProducts);
        lblLowStockCount.setText("Low Stock Items: " + lowStockCount);
        lblTotalValue.setText("Total Inventory Value: " + currencyFormat.format(totalValue));
        
        // Update low stock button style
        if (lowStockCount > 0) {
            btnLowStockReport.getStyleClass().removeAll("button", "warning");
            btnLowStockReport.getStyleClass().addAll("button", "warning");
        }
    }

    // Event Handlers
    @FXML
    private void handleAddProduct() {
        // TODO: Open product form dialog
        AlertUtil.showInfo("Feature Coming Soon", "Add Product dialog will be implemented next.");
    }

    @FXML
    private void handleRefresh() {
        loadProducts();
        loadCategories();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handleCategoryFilter() {
        applyFilters();
    }

    @FXML
    private void handleStockFilter() {
        applyFilters();
    }

    @FXML
    private void handleClearFilters() {
        txtSearch.clear();
        cmbCategory.setValue("All Categories");
        cmbStockStatus.setValue("All");
        applyFilters();
    }

    @FXML
    private void handleEditProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleEditProduct(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to edit.");
        }
    }

    private void handleEditProduct(Product product) {
        // TODO: Open product edit dialog
        AlertUtil.showInfo("Feature Coming Soon", "Edit Product dialog for: " + product.getName());
    }

    @FXML
    private void handleDuplicateProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            AlertUtil.showInfo("Feature Coming Soon", "Duplicate Product feature for: " + selected.getName());
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to duplicate.");
        }
    }

    @FXML
    private void handleAdjustStock() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleAdjustStock(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to adjust stock.");
        }
    }

    private void handleAdjustStock(Product product) {
        String input = AlertUtil.showTextInput("Adjust Stock", 
            "Current stock: " + product.getStockQuantity(),
            "Enter new stock quantity:", 
            String.valueOf(product.getStockQuantity())).orElse(null);
        
        if (input != null) {
            try {
                int newQuantity = Integer.parseInt(input);
                if (newQuantity < 0) {
                    AlertUtil.showError("Invalid Input", "Stock quantity cannot be negative.");
                    return;
                }
                
                productDAO.updateStock(product.getId(), newQuantity);
                product.setStockQuantity(newQuantity);
                tblProducts.refresh();
                updateSummary();
                
                AlertUtil.showSuccess("Stock Updated", 
                    "Stock for " + product.getName() + " updated to " + newQuantity);
                
            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Input", "Please enter a valid number.");
            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to update stock: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleViewHistory() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            AlertUtil.showInfo("Feature Coming Soon", "View History for: " + selected.getName());
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to view history.");
        }
    }

    @FXML
    private void handleDeleteProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (AlertUtil.showConfirmation("Delete Product", 
                "Are you sure you want to delete: " + selected.getName() + "?")) {
                try {
                    productDAO.delete(selected.getId());
                    allProducts.remove(selected);
                    applyFilters();
                    AlertUtil.showSuccess("Product Deleted", selected.getName() + " has been deleted.");
                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to delete product: " + e.getMessage());
                }
            }
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to delete.");
        }
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Feature Coming Soon", "Export functionality will be implemented next.");
    }

    @FXML
    private void handleLowStockReport() {
        try {
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            if (lowStockProducts.isEmpty()) {
                AlertUtil.showInfo("Low Stock Report", "No products are currently low on stock.");
            } else {
                StringBuilder report = new StringBuilder("Low Stock Products:\n\n");
                for (Product product : lowStockProducts) {
                    report.append(String.format("• %s (%s) - Stock: %d, Min: %d\n", 
                        product.getName(), product.getSku(), 
                        product.getStockQuantity(), product.getMinStockLevel()));
                }
                AlertUtil.showWarning("Low Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate low stock report: " + e.getMessage());
        }
    }
}

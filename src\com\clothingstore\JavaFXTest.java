package com.clothingstore;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;

/**
 * Simple JavaFX Test Application
 * Tests if JavaFX runtime is working properly
 */
public class JavaFXTest extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Create simple test interface
            VBox root = new VBox(20);
            root.setPadding(new Insets(30));
            root.setAlignment(Pos.CENTER);
            root.setStyle("-fx-background-color: #f0f8ff;");
            
            // Title
            Label title = new Label("JavaFX Runtime Test");
            title.setFont(Font.font("System", FontWeight.BOLD, 24));
            title.setStyle("-fx-text-fill: #2c3e50;");
            
            // Status
            Label status = new Label("✅ JavaFX is working correctly!");
            status.setFont(Font.font("System", FontWeight.NORMAL, 16));
            status.setStyle("-fx-text-fill: #27ae60;");
            
            // Test button
            Button testBtn = new Button("Test Button - Click Me!");
            testBtn.setPrefWidth(200);
            testBtn.setPrefHeight(40);
            testBtn.setStyle(
                "-fx-background-color: #3498db; " +
                "-fx-text-fill: white; " +
                "-fx-font-weight: bold; " +
                "-fx-background-radius: 5;"
            );
            testBtn.setOnAction(e -> {
                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("JavaFX Test");
                alert.setHeaderText("Success!");
                alert.setContentText("JavaFX runtime is working perfectly!\n\nYou can now run the full Clothing Store POS System.");
                alert.showAndWait();
            });
            
            // Info
            Label info = new Label("If you can see this window, JavaFX runtime is installed correctly.");
            info.setStyle("-fx-text-fill: #7f8c8d;");
            
            Button closeBtn = new Button("Close Test");
            closeBtn.setOnAction(e -> primaryStage.close());
            closeBtn.setStyle(
                "-fx-background-color: #95a5a6; " +
                "-fx-text-fill: white; " +
                "-fx-background-radius: 5;"
            );
            
            root.getChildren().addAll(title, status, testBtn, info, closeBtn);
            
            Scene scene = new Scene(root, 400, 300);
            primaryStage.setTitle("JavaFX Runtime Test");
            primaryStage.setScene(scene);
            primaryStage.setResizable(false);
            primaryStage.show();
            
        } catch (Exception e) {
            System.err.println("JavaFX Test Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}

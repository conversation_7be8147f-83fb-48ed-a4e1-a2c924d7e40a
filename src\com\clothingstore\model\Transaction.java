package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Transaction model class representing sales transactions
 */
public class Transaction {
    private Long id;
    private String transactionNumber;
    private Long customerId;
    private Customer customer;
    private LocalDateTime transactionDate;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private String paymentMethod; // CASH, CREDIT_CARD, DEBIT_CARD, GIFT_CARD
    private String status; // COMPLETED, REFUNDED, PARTIALLY_REFUNDED, CANCELLED
    private String notes;
    private List<TransactionItem> items;
    private String cashierName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public Transaction() {
        this.transactionDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = "COMPLETED";
        this.items = new ArrayList<>();
        this.subtotal = BigDecimal.ZERO;
        this.taxAmount = BigDecimal.ZERO;
        this.discountAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    public Transaction(String transactionNumber, Customer customer) {
        this();
        this.transactionNumber = transactionNumber;
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getTransactionNumber() { return transactionNumber; }
    public void setTransactionNumber(String transactionNumber) { this.transactionNumber = transactionNumber; }

    public Long getCustomerId() { return customerId; }
    public void setCustomerId(Long customerId) { this.customerId = customerId; }

    public Customer getCustomer() { return customer; }
    public void setCustomer(Customer customer) { 
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    public LocalDateTime getTransactionDate() { return transactionDate; }
    public void setTransactionDate(LocalDateTime transactionDate) { this.transactionDate = transactionDate; }

    public BigDecimal getSubtotal() { return subtotal; }
    public void setSubtotal(BigDecimal subtotal) { this.subtotal = subtotal; }

    public BigDecimal getTaxAmount() { return taxAmount; }
    public void setTaxAmount(BigDecimal taxAmount) { this.taxAmount = taxAmount; }

    public BigDecimal getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(BigDecimal discountAmount) { this.discountAmount = discountAmount; }

    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }

    public String getStatus() { return status; }
    public void setStatus(String status) { 
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public List<TransactionItem> getItems() { return items; }
    public void setItems(List<TransactionItem> items) { this.items = items; }

    public String getCashierName() { return cashierName; }
    public void setCashierName(String cashierName) { this.cashierName = cashierName; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    // Business methods
    public void addItem(TransactionItem item) {
        items.add(item);
        item.setTransaction(this);
        recalculateAmounts();
    }

    public void removeItem(TransactionItem item) {
        items.remove(item);
        recalculateAmounts();
    }

    public void recalculateAmounts() {
        subtotal = items.stream()
                .map(TransactionItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // No automatic discount application - discounts must be manually set
        // discountAmount is only applied if manually set via setDiscountAmount()

        // No tax calculation - set tax to zero
        taxAmount = BigDecimal.ZERO;

        // Calculate total (subtotal minus manually applied discount, no tax)
        totalAmount = subtotal.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Manually apply a discount amount
     */
    public void applyManualDiscount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
        recalculateAmounts();
    }

    /**
     * Manually apply a discount percentage
     */
    public void applyManualDiscountPercentage(double discountPercentage) {
        if (discountPercentage >= 0.0 && discountPercentage <= 1.0) {
            this.discountAmount = subtotal.multiply(BigDecimal.valueOf(discountPercentage));
            recalculateAmounts();
        }
    }

    /**
     * Clear any applied discount
     */
    public void clearDiscount() {
        this.discountAmount = BigDecimal.ZERO;
        recalculateAmounts();
    }

    public int getTotalItemCount() {
        return items.stream().mapToInt(TransactionItem::getQuantity).sum();
    }

    public boolean canBeRefunded() {
        return "COMPLETED".equals(status);
    }

    public void processRefund() {
        if (!canBeRefunded()) {
            throw new IllegalStateException("Transaction cannot be refunded");
        }
        this.status = "REFUNDED";
        this.updatedAt = LocalDateTime.now();
    }

    public void processPartialRefund(BigDecimal refundAmount) {
        if (!canBeRefunded() && !"PARTIALLY_REFUNDED".equals(status)) {
            throw new IllegalStateException("Transaction cannot be refunded");
        }
        this.status = "PARTIALLY_REFUNDED";
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Transaction that = (Transaction) o;
        return Objects.equals(id, that.id) && Objects.equals(transactionNumber, that.transactionNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, transactionNumber);
    }

    @Override
    public String toString() {
        return String.format("Transaction{id=%d, number='%s', date=%s, total=%s, status='%s'}",
                id, transactionNumber, transactionDate, totalAmount, status);
    }
}

package com.clothingstore.view;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.util.FormatUtil;

import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * Controller for Customer Management interface
 */
public class CustomerManagementController implements Initializable {

    @FXML private TextField txtSearch;
    @FXML private ComboBox<String> cmbMembership;
    @FXML private ComboBox<String> cmbStatus;
    @FXML private Button btnAddCustomer;
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnExport;
    @FXML private Button btnLoyaltyReport;
    @FXML private Button btnBirthdayReport;

    @FXML private TableView<Customer> tblCustomers;
    @FXML private TableColumn<Customer, String> colName;
    @FXML private TableColumn<Customer, String> colEmail;
    @FXML private TableColumn<Customer, String> colPhone;
    @FXML private TableColumn<Customer, String> colMembership;
    @FXML private TableColumn<Customer, Integer> colPoints;
    @FXML private TableColumn<Customer, String> colTotalSpent;
    @FXML private TableColumn<Customer, Integer> colTotalPurchases;
    @FXML private TableColumn<Customer, String> colLastPurchase;
    @FXML private TableColumn<Customer, String> colStatus;
    @FXML private TableColumn<Customer, String> colActions;

    @FXML private MenuItem menuEdit;
    @FXML private MenuItem menuViewHistory;
    @FXML private MenuItem menuAdjustPoints;
    @FXML private MenuItem menuSendEmail;
    @FXML private MenuItem menuDeactivate;

    @FXML private Label lblTotalCustomers;
    @FXML private Label lblActiveMembers;
    @FXML private Label lblTotalPoints;
    @FXML private Label lblAverageSpent;
    @FXML private Label lblFilteredCount;

    private ObservableList<Customer> allCustomers;
    private ObservableList<Customer> filteredCustomers;
    private CustomerDAO customerDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        customerDAO = CustomerDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        
        allCustomers = FXCollections.observableArrayList();
        filteredCustomers = FXCollections.observableArrayList();
        
        setupTableColumns();
        setupFilters();
        loadCustomers();
        updateStatistics();
    }

    private void setupTableColumns() {
        colName.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFullName()));
        colEmail.setCellValueFactory(new PropertyValueFactory<>("email"));
        colPhone.setCellValueFactory(cellData -> 
            new SimpleStringProperty(FormatUtil.formatPhone(cellData.getValue().getPhone())));
        colMembership.setCellValueFactory(cellData -> 
            new SimpleStringProperty(FormatUtil.formatMembershipLevel(cellData.getValue().getMembershipLevel())));
        colPoints.setCellValueFactory(new PropertyValueFactory<>("loyaltyPoints"));
        colTotalPurchases.setCellValueFactory(new PropertyValueFactory<>("totalPurchases"));
        
        colTotalSpent.setCellValueFactory(cellData -> 
            new SimpleStringProperty(FormatUtil.formatCurrency(cellData.getValue().getTotalSpent())));
        
        colLastPurchase.setCellValueFactory(cellData -> {
            LocalDateTime lastPurchase = cellData.getValue().getLastPurchaseDate();
            return new SimpleStringProperty(lastPurchase != null ? 
                FormatUtil.formatDate(lastPurchase.toLocalDate()) : "Never");
        });
        
        colStatus.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().isActive() ? "Active" : "Inactive"));
        
        // Style membership column based on level
        colMembership.setCellFactory(col -> new TableCell<Customer, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    switch (item.toUpperCase()) {
                        case "PLATINUM":
                            setStyle("-fx-text-fill: #9b59b6; -fx-font-weight: bold;");
                            break;
                        case "GOLD":
                            setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                            break;
                        case "SILVER":
                            setStyle("-fx-text-fill: #95a5a6; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #8b4513; -fx-font-weight: bold;");
                    }
                }
            }
        });
        
        // Action buttons column
        colActions.setCellFactory(col -> new TableCell<Customer, String>() {
            private final Button editBtn = new Button("Edit");
            private final Button historyBtn = new Button("History");
            
            {
                editBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    handleEditCustomer(customer);
                });
                
                historyBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    handleViewHistory(customer);
                });
                
                editBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                historyBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new javafx.scene.layout.HBox(2, editBtn, historyBtn));
                }
            }
        });
        
        tblCustomers.setItems(filteredCustomers);
    }

    private void setupFilters() {
        // Membership filter
        cmbMembership.setItems(FXCollections.observableArrayList(
            "All Levels", "BRONZE", "SILVER", "GOLD", "PLATINUM"));
        cmbMembership.setValue("All Levels");
        
        // Status filter
        cmbStatus.setItems(FXCollections.observableArrayList("All", "Active", "Inactive"));
        cmbStatus.setValue("All");
        
        // Add listeners for real-time filtering
        filteredCustomers.addListener((javafx.collections.ListChangeListener<Customer>) change -> {
            updateFilteredCount();
        });
    }

    private void loadCustomers() {
        try {
            List<Customer> customers = customerDAO.findAll();
            allCustomers.setAll(customers);
            applyFilters();
        } catch (SQLException e) {
            showError("Database Error", "Failed to load customers: " + e.getMessage());
        }
    }

    private void applyFilters() {
        List<Customer> filtered = allCustomers.stream()
            .filter(this::matchesSearchFilter)
            .filter(this::matchesMembershipFilter)
            .filter(this::matchesStatusFilter)
            .collect(Collectors.toList());
        
        filteredCustomers.setAll(filtered);
        updateStatistics();
    }

    private boolean matchesSearchFilter(Customer customer) {
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }
        
        String term = searchTerm.toLowerCase();
        return (customer.getFirstName() != null && customer.getFirstName().toLowerCase().contains(term)) ||
               (customer.getLastName() != null && customer.getLastName().toLowerCase().contains(term)) ||
               (customer.getEmail() != null && customer.getEmail().toLowerCase().contains(term)) ||
               (customer.getPhone() != null && customer.getPhone().contains(term));
    }

    private boolean matchesMembershipFilter(Customer customer) {
        String selectedMembership = cmbMembership.getValue();
        return selectedMembership == null || "All Levels".equals(selectedMembership) ||
               selectedMembership.equals(customer.getMembershipLevel());
    }

    private boolean matchesStatusFilter(Customer customer) {
        String status = cmbStatus.getValue();
        if (status == null || "All".equals(status)) {
            return true;
        }
        
        switch (status) {
            case "Active":
                return customer.isActive();
            case "Inactive":
                return !customer.isActive();
            default:
                return true;
        }
    }

    private void updateStatistics() {
        int totalCustomers = allCustomers.size();
        int activeMembers = (int) allCustomers.stream().filter(Customer::isActive).count();
        int totalPoints = allCustomers.stream().mapToInt(Customer::getLoyaltyPoints).sum();
        double averageSpent = allCustomers.stream().mapToDouble(Customer::getTotalSpent).average().orElse(0.0);
        
        lblTotalCustomers.setText(String.valueOf(totalCustomers));
        lblActiveMembers.setText(String.valueOf(activeMembers));
        lblTotalPoints.setText(FormatUtil.formatNumber(totalPoints));
        lblAverageSpent.setText(FormatUtil.formatCurrency(averageSpent));
    }

    private void updateFilteredCount() {
        lblFilteredCount.setText("Showing: " + filteredCustomers.size() + " customers");
    }

    // Event Handlers
    @FXML
    private void handleAddCustomer() {
        showInfo("Feature Coming Soon", "Add Customer dialog will be implemented next.");
    }

    @FXML
    private void handleRefresh() {
        loadCustomers();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handleMembershipFilter() {
        applyFilters();
    }

    @FXML
    private void handleStatusFilter() {
        applyFilters();
    }

    @FXML
    private void handleClearFilters() {
        txtSearch.clear();
        cmbMembership.setValue("All Levels");
        cmbStatus.setValue("All");
        applyFilters();
    }

    @FXML
    private void handleEditCustomer() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleEditCustomer(selected);
        } else {
            showWarning("No Selection", "Please select a customer to edit.");
        }
    }

    private void handleEditCustomer(Customer customer) {
        showInfo("Feature Coming Soon", "Edit Customer dialog for: " + customer.getFullName());
    }

    @FXML
    private void handleViewHistory() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewHistory(selected);
        } else {
            showWarning("No Selection", "Please select a customer to view history.");
        }
    }

    private void handleViewHistory(Customer customer) {
        showInfo("Feature Coming Soon", "Purchase history for: " + customer.getFullName());
    }

    @FXML
    private void handleAdjustPoints() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            showInfo("Feature Coming Soon", "Adjust loyalty points for: " + selected.getFullName());
        } else {
            showWarning("No Selection", "Please select a customer to adjust points.");
        }
    }

    @FXML
    private void handleSendEmail() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            showInfo("Feature Coming Soon", "Send email to: " + selected.getEmail());
        } else {
            showWarning("No Selection", "Please select a customer to send email.");
        }
    }

    @FXML
    private void handleDeactivateCustomer() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (showConfirmation("Deactivate Customer", 
                "Are you sure you want to deactivate: " + selected.getFullName() + "?")) {
                try {
                    customerDAO.delete(selected.getId());
                    loadCustomers();
                    showSuccess("Customer Deactivated", selected.getFullName() + " has been deactivated.");
                } catch (SQLException e) {
                    showError("Database Error", "Failed to deactivate customer: " + e.getMessage());
                }
            }
        } else {
            showWarning("No Selection", "Please select a customer to deactivate.");
        }
    }

    @FXML
    private void handleExport() {
        showInfo("Feature Coming Soon", "Export functionality will be implemented next.");
    }

    @FXML
    private void handleLoyaltyReport() {
        try {
            List<Customer> topCustomers = customerDAO.findTopCustomers(10);
            StringBuilder report = new StringBuilder("Top 10 Loyalty Customers:\n\n");
            for (int i = 0; i < topCustomers.size(); i++) {
                Customer c = topCustomers.get(i);
                report.append(String.format("%d. %s - %s (%s, %d points)\n", 
                    i+1, c.getFullName(), FormatUtil.formatCurrency(c.getTotalSpent()), 
                    c.getMembershipLevel(), c.getLoyaltyPoints()));
            }
            showInfo("Loyalty Report", report.toString());
        } catch (SQLException e) {
            showError("Database Error", "Failed to generate loyalty report: " + e.getMessage());
        }
    }

    @FXML
    private void handleBirthdayReport() {
        showInfo("Feature Coming Soon", "Birthday report will be implemented next.");
    }

    // Utility methods for alerts (simplified for demo)
    private void showInfo(String title, String message) {
        System.out.println("INFO - " + title + ": " + message);
    }

    private void showWarning(String title, String message) {
        System.out.println("WARNING - " + title + ": " + message);
    }

    private void showError(String title, String message) {
        System.err.println("ERROR - " + title + ": " + message);
    }

    private void showSuccess(String title, String message) {
        System.out.println("SUCCESS - " + title + ": " + message);
    }

    private boolean showConfirmation(String title, String message) {
        System.out.print("CONFIRM - " + title + ": " + message + " (y/n): ");
        return true; // For demo purposes, always return true
    }
}

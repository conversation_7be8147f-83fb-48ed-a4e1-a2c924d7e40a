package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Data Access Object for Transaction operations
 */
public class TransactionDAO {
    
    private static TransactionDAO instance;
    
    private TransactionDAO() {}
    
    public static synchronized TransactionDAO getInstance() {
        if (instance == null) {
            instance = new TransactionDAO();
        }
        return instance;
    }
    
    public List<Transaction> findAll() throws SQLException {
        String sql = "SELECT * FROM transactions ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                Transaction transaction = mapResultSetToTransaction(rs);
                loadTransactionItems(transaction);
                transactions.add(transaction);
            }
        }
        
        return transactions;
    }
    
    public Optional<Transaction> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    return Optional.of(transaction);
                }
            }
        }
        
        return Optional.empty();
    }
    
    public Optional<Transaction> findByTransactionNumber(String transactionNumber) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE transaction_number = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, transactionNumber);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    return Optional.of(transaction);
                }
            }
        }
        
        return Optional.empty();
    }
    
    public List<Transaction> findByCustomerId(Long customerId) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE customer_id = ? ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, customerId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    transactions.add(transaction);
                }
            }
        }
        
        return transactions;
    }
    
    public List<Transaction> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE transaction_date BETWEEN ? AND ? ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    transactions.add(transaction);
                }
            }
        }
        
        return transactions;
    }
    
    public Transaction save(Transaction transaction) throws SQLException {
        Connection conn = DatabaseManager.getInstance().getConnection();
        conn.setAutoCommit(false);
        
        try {
            if (transaction.getId() == null) {
                transaction = insertTransaction(conn, transaction);
            } else {
                transaction = updateTransaction(conn, transaction);
            }
            
            // Save transaction items
            saveTransactionItems(conn, transaction);
            
            conn.commit();
            return transaction;
            
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }
    
    private Transaction insertTransaction(Connection conn, Transaction transaction) throws SQLException {
        String sql = """
            INSERT INTO transactions (transaction_number, customer_id, transaction_date, subtotal, 
                                    tax_amount, discount_amount, total_amount, payment_method, 
                                    status, notes, cashier_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            setPreparedStatementParameters(pstmt, transaction);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating transaction failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    transaction.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating transaction failed, no ID obtained.");
                }
            }
        }
        
        return transaction;
    }
    
    private Transaction updateTransaction(Connection conn, Transaction transaction) throws SQLException {
        String sql = """
            UPDATE transactions SET transaction_number = ?, customer_id = ?, transaction_date = ?, 
                                  subtotal = ?, tax_amount = ?, discount_amount = ?, total_amount = ?, 
                                  payment_method = ?, status = ?, notes = ?, cashier_name = ?, 
                                  updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            setPreparedStatementParameters(pstmt, transaction);
            pstmt.setLong(12, transaction.getId());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating transaction failed, no rows affected.");
            }
        }
        
        return transaction;
    }
    
    private void saveTransactionItems(Connection conn, Transaction transaction) throws SQLException {
        // Delete existing items
        String deleteSql = "DELETE FROM transaction_items WHERE transaction_id = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {
            pstmt.setLong(1, transaction.getId());
            pstmt.executeUpdate();
        }
        
        // Insert new items
        String insertSql = """
            INSERT INTO transaction_items (transaction_id, product_id, quantity, unit_price, 
                                         line_total, discount_amount, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS)) {
            for (TransactionItem item : transaction.getItems()) {
                pstmt.setLong(1, transaction.getId());
                pstmt.setLong(2, item.getProductId());
                pstmt.setInt(3, item.getQuantity());
                pstmt.setBigDecimal(4, item.getUnitPrice());
                pstmt.setBigDecimal(5, item.getLineTotal());
                pstmt.setBigDecimal(6, item.getDiscountAmount());
                pstmt.setString(7, item.getNotes());
                
                pstmt.addBatch();
            }
            
            pstmt.executeBatch();
            
            // Get generated IDs for items
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                int index = 0;
                while (generatedKeys.next() && index < transaction.getItems().size()) {
                    transaction.getItems().get(index).setId(generatedKeys.getLong(1));
                    index++;
                }
            }
        }
    }
    
    private void loadTransactionItems(Transaction transaction) throws SQLException {
        String sql = """
            SELECT ti.*, p.name as product_name, p.sku as product_sku 
            FROM transaction_items ti 
            LEFT JOIN products p ON ti.product_id = p.id 
            WHERE ti.transaction_id = ?
        """;
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, transaction.getId());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                List<TransactionItem> items = new ArrayList<>();
                while (rs.next()) {
                    TransactionItem item = new TransactionItem();
                    item.setId(rs.getLong("id"));
                    item.setTransactionId(rs.getLong("transaction_id"));
                    item.setProductId(rs.getLong("product_id"));
                    item.setQuantity(rs.getInt("quantity"));
                    item.setUnitPrice(rs.getBigDecimal("unit_price"));
                    item.setLineTotal(rs.getBigDecimal("line_total"));
                    item.setDiscountAmount(rs.getBigDecimal("discount_amount"));
                    item.setNotes(rs.getString("notes"));
                    
                    items.add(item);
                }
                transaction.setItems(items);
            }
        }
    }
    
    public void delete(Long id) throws SQLException {
        Connection conn = DatabaseManager.getInstance().getConnection();
        conn.setAutoCommit(false);
        
        try {
            // Delete transaction items first
            String deleteItemsSql = "DELETE FROM transaction_items WHERE transaction_id = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(deleteItemsSql)) {
                pstmt.setLong(1, id);
                pstmt.executeUpdate();
            }
            
            // Delete transaction
            String deleteTransactionSql = "DELETE FROM transactions WHERE id = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(deleteTransactionSql)) {
                pstmt.setLong(1, id);
                int affectedRows = pstmt.executeUpdate();
                if (affectedRows == 0) {
                    throw new SQLException("Deleting transaction failed, no rows affected.");
                }
            }
            
            conn.commit();
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }
    
    public String generateTransactionNumber() {
        return "TXN" + System.currentTimeMillis();
    }
    
    private void setPreparedStatementParameters(PreparedStatement pstmt, Transaction transaction) throws SQLException {
        pstmt.setString(1, transaction.getTransactionNumber());
        pstmt.setObject(2, transaction.getCustomerId());
        pstmt.setTimestamp(3, Timestamp.valueOf(transaction.getTransactionDate()));
        pstmt.setBigDecimal(4, transaction.getSubtotal());
        pstmt.setBigDecimal(5, transaction.getTaxAmount());
        pstmt.setBigDecimal(6, transaction.getDiscountAmount());
        pstmt.setBigDecimal(7, transaction.getTotalAmount());
        pstmt.setString(8, transaction.getPaymentMethod());
        pstmt.setString(9, transaction.getStatus());
        pstmt.setString(10, transaction.getNotes());
        pstmt.setString(11, transaction.getCashierName());
    }
    
    private Transaction mapResultSetToTransaction(ResultSet rs) throws SQLException {
        Transaction transaction = new Transaction();
        transaction.setId(rs.getLong("id"));
        transaction.setTransactionNumber(rs.getString("transaction_number"));
        transaction.setCustomerId(rs.getObject("customer_id", Long.class));
        
        Timestamp transactionDate = rs.getTimestamp("transaction_date");
        if (transactionDate != null) {
            transaction.setTransactionDate(transactionDate.toLocalDateTime());
        }
        
        transaction.setSubtotal(rs.getBigDecimal("subtotal"));
        transaction.setTaxAmount(rs.getBigDecimal("tax_amount"));
        transaction.setDiscountAmount(rs.getBigDecimal("discount_amount"));
        transaction.setTotalAmount(rs.getBigDecimal("total_amount"));
        transaction.setPaymentMethod(rs.getString("payment_method"));
        transaction.setStatus(rs.getString("status"));
        transaction.setNotes(rs.getString("notes"));
        transaction.setCashierName(rs.getString("cashier_name"));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            transaction.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            transaction.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return transaction;
    }
}

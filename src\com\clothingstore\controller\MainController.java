package com.clothingstore.controller;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.geometry.Insets;
import javafx.geometry.Pos;

import com.clothingstore.gui.SwingPOSDemo;
import com.clothingstore.demo.SimplePOSDemo;
import com.clothingstore.demo.ManualDiscountDemo;
import com.clothingstore.test.ComprehensiveTestSuite;

import java.net.URL;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Main Controller for the JavaFX Application
 */
public class MainController implements Initializable {

    @FXML private TabPane mainTabPane;
    @FXML private Tab dashboardTab;
    @FXML private Label statusLabel;
    @FXML private Label timeLabel;

    private Timer clockTimer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupClock();
        statusLabel.setText("Clothing Store POS System Ready - Manual Discount Controls Active");
    }

    private void setupClock() {
        clockTimer = new Timer(true);
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    timeLabel.setText(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        }, 0, 1000);
    }

    @FXML
    private void handleExit() {
        if (clockTimer != null) {
            clockTimer.cancel();
        }
        Platform.exit();
    }

    @FXML
    private void showPOSSystem() {
        Tab posTab = findOrCreateTab("Point of Sale", "pos-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Point of Sale System");
        title.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        Label subtitle = new Label("Manual Discount Control System");
        subtitle.setStyle("-fx-font-size: 16px; -fx-font-style: italic;");
        
        VBox features = new VBox(10);
        features.setAlignment(Pos.CENTER_LEFT);
        features.getChildren().addAll(
            new Label("✓ No automatic discounts - full cashier control"),
            new Label("✓ Manual percentage and dollar amount discounts"),
            new Label("✓ Real-time inventory updates"),
            new Label("✓ Customer loyalty integration"),
            new Label("✓ Professional transaction processing")
        );
        
        HBox buttons = new HBox(15);
        buttons.setAlignment(Pos.CENTER);
        
        Button swingPOSBtn = new Button("Launch Swing POS Interface");
        swingPOSBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        swingPOSBtn.setOnAction(e -> {
            new Thread(() -> SwingPOSDemo.main(new String[]{})).start();
            statusLabel.setText("Swing POS Interface Launched");
        });
        
        Button consoleDemoBtn = new Button("Run Console Demo");
        consoleDemoBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        consoleDemoBtn.setOnAction(e -> {
            new Thread(() -> SimplePOSDemo.main(new String[]{})).start();
            statusLabel.setText("Console Demo Started");
        });
        
        Button discountDemoBtn = new Button("Manual Discount Demo");
        discountDemoBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        discountDemoBtn.setOnAction(e -> {
            new Thread(() -> ManualDiscountDemo.main(new String[]{})).start();
            statusLabel.setText("Manual Discount Demo Started");
        });
        
        buttons.getChildren().addAll(swingPOSBtn, consoleDemoBtn, discountDemoBtn);
        
        content.getChildren().addAll(title, subtitle, new Separator(), features, new Separator(), buttons);
        posTab.setContent(content);
        
        mainTabPane.getSelectionModel().select(posTab);
    }

    @FXML
    private void showInventoryManagement() {
        Tab inventoryTab = findOrCreateTab("Inventory Management", "inventory-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Inventory Management");
        title.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Current Inventory Status:"),
            new Label("• 10 Products across 5 categories"),
            new Label("• Real-time stock tracking"),
            new Label("• Automatic low stock alerts"),
            new Label("• Category-based organization"),
            new Label("• Stock adjustment capabilities")
        );
        
        Button viewInventoryBtn = new Button("View Current Inventory");
        viewInventoryBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        viewInventoryBtn.setOnAction(e -> {
            statusLabel.setText("Inventory data available through POS system");
        });
        
        content.getChildren().addAll(title, new Separator(), info, viewInventoryBtn);
        inventoryTab.setContent(content);
        
        mainTabPane.getSelectionModel().select(inventoryTab);
    }

    @FXML
    private void showCustomerManagement() {
        Tab customerTab = findOrCreateTab("Customer Management", "customer-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Customer Management");
        title.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Customer Loyalty Program:"),
            new Label("• 4-tier membership system (Bronze/Silver/Gold/Platinum)"),
            new Label("• Manual loyalty point management"),
            new Label("• Purchase history tracking"),
            new Label("• Suggested discounts (manual application only)"),
            new Label("• Customer search and selection")
        );
        
        Button viewCustomersBtn = new Button("Access Customer Database");
        viewCustomersBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        viewCustomersBtn.setOnAction(e -> {
            statusLabel.setText("Customer management available through POS system");
        });
        
        content.getChildren().addAll(title, new Separator(), info, viewCustomersBtn);
        customerTab.setContent(content);
        
        mainTabPane.getSelectionModel().select(customerTab);
    }

    @FXML
    private void showSalesReport() {
        Tab reportsTab = findOrCreateTab("Reports & Analytics", "reports-tab");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);
        
        Label title = new Label("Reports & Analytics");
        title.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        VBox info = new VBox(10);
        info.setAlignment(Pos.CENTER_LEFT);
        info.getChildren().addAll(
            new Label("Available Reports:"),
            new Label("• Customer ranking by spending"),
            new Label("• Inventory valuation reports"),
            new Label("• Low stock monitoring"),
            new Label("• Transaction history"),
            new Label("• Membership distribution analytics"),
            new Label("• Manual discount usage tracking")
        );
        
        Button runTestsBtn = new Button("Run System Tests");
        runTestsBtn.setStyle("-fx-font-size: 14px; -fx-padding: 10 20;");
        runTestsBtn.setOnAction(e -> {
            new Thread(() -> ComprehensiveTestSuite.main(new String[]{})).start();
            statusLabel.setText("System tests running...");
        });
        
        content.getChildren().addAll(title, new Separator(), info, runTestsBtn);
        reportsTab.setContent(content);
        
        mainTabPane.getSelectionModel().select(reportsTab);
    }

    @FXML
    private void showLowStockReport() {
        statusLabel.setText("Low stock report functionality available through inventory management");
    }

    @FXML
    private void showCustomerReport() {
        statusLabel.setText("Customer reports available through customer management");
    }

    @FXML
    private void showAbout() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About");
        alert.setHeaderText("Clothing Store POS & Inventory Management System");
        alert.setContentText(
            "Version 1.0\n\n" +
            "Features:\n" +
            "• Manual Discount Control System\n" +
            "• Real-time Inventory Management\n" +
            "• Customer Loyalty Program\n" +
            "• Professional Point of Sale Interface\n" +
            "• Comprehensive Reporting\n\n" +
            "Built with Java and JavaFX\n" +
            "Database: SQLite\n" +
            "Architecture: Model-View-Controller (MVC)"
        );
        alert.showAndWait();
    }

    private Tab findOrCreateTab(String title, String id) {
        for (Tab tab : mainTabPane.getTabs()) {
            if (id.equals(tab.getId())) {
                return tab;
            }
        }
        
        Tab newTab = new Tab(title);
        newTab.setId(id);
        newTab.setClosable(true);
        mainTabPane.getTabs().add(newTab);
        return newTab;
    }
}

package com.clothingstore.gui;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Scanner;

/**
 * Console-based GUI simulation showing the interface layout and functionality
 */
public class ConsoleGUIDemo {
    
    private static ProductDAO productDAO;
    private static CustomerDAO customerDAO;
    private static TransactionService transactionService;
    private static NumberFormat currency;
    private static Scanner scanner;
    
    private static Transaction currentTransaction;
    private static Customer selectedCustomer;
    
    public static void main(String[] args) {
        System.out.println("CLOTHING STORE POS SYSTEM - GUI SIMULATION");
        System.out.println("===========================================");
        
        try {
            // Initialize system
            DatabaseManager.getInstance().initializeDatabase();
            productDAO = ProductDAO.getInstance();
            customerDAO = CustomerDAO.getInstance();
            transactionService = TransactionService.getInstance();
            currency = NumberFormat.getCurrencyInstance();
            scanner = new Scanner(System.in);
            
            startNewTransaction();
            showMainInterface();
            
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
            if (scanner != null) scanner.close();
        }
    }
    
    private static void startNewTransaction() {
        currentTransaction = new Transaction();
        currentTransaction.setCashierName("Demo Cashier");
        selectedCustomer = null;
    }
    
    private static void showMainInterface() throws SQLException {
        while (true) {
            clearScreen();
            showHeader();
            showCustomerSection();
            showProductsAndCart();
            showDiscountSection();
            showTotalsSection();
            showMenu();
            
            String choice = scanner.nextLine().trim();
            
            switch (choice.toLowerCase()) {
                case "1":
                    searchCustomer();
                    break;
                case "2":
                    addProductToCart();
                    break;
                case "3":
                    removeFromCart();
                    break;
                case "4":
                    applyManualDiscount();
                    break;
                case "5":
                    clearDiscount();
                    break;
                case "6":
                    processPayment();
                    break;
                case "7":
                    startNewTransaction();
                    break;
                case "0":
                    System.out.println("Thank you for using the POS system!");
                    return;
                default:
                    System.out.println("Invalid option. Press Enter to continue...");
                    scanner.nextLine();
            }
        }
    }
    
    private static void clearScreen() {
        // Simulate clearing screen
        for (int i = 0; i < 50; i++) {
            System.out.println();
        }
    }
    
    private static void showHeader() {
        System.out.println("+" + "=".repeat(80) + "+");
        System.out.println("|" + " ".repeat(25) + "CLOTHING STORE POS SYSTEM" + " ".repeat(29) + "|");
        System.out.println("|" + " ".repeat(28) + "Manual Discount Demo" + " ".repeat(32) + "|");
        System.out.println("+" + "=".repeat(80) + "+");
    }
    
    private static void showCustomerSection() {
        System.out.println("\n[CUSTOMER INFORMATION]");
        System.out.println("-".repeat(50));
        if (selectedCustomer == null) {
            System.out.println("No customer selected");
        } else {
            System.out.printf("Customer: %s (%s)\n", selectedCustomer.getFullName(), selectedCustomer.getMembershipLevel());
            System.out.printf("Loyalty Points: %d | Suggested Discount: %.0f%%\n", 
                selectedCustomer.getLoyaltyPoints(), selectedCustomer.getSuggestedDiscountPercentage() * 100);
        }
    }
    
    private static void showProductsAndCart() throws SQLException {
        System.out.println("\n[PRODUCTS]" + " ".repeat(35) + "[SHOPPING CART]");
        System.out.println("-".repeat(40) + " " + "-".repeat(39));
        
        List<Product> products = productDAO.findAll();
        List<TransactionItem> cartItems = currentTransaction.getItems();
        
        int maxRows = Math.max(Math.min(products.size(), 8), cartItems.size());
        
        for (int i = 0; i < maxRows; i++) {
            String productLine = "";
            String cartLine = "";
            
            if (i < products.size() && i < 8) {
                Product p = products.get(i);
                productLine = String.format("%-8s %-20s %8s %3d", 
                    p.getSku(), 
                    p.getName().length() > 20 ? p.getName().substring(0, 17) + "..." : p.getName(),
                    currency.format(p.getPrice()), 
                    p.getStockQuantity());
            } else {
                productLine = " ".repeat(40);
            }
            
            if (i < cartItems.size()) {
                TransactionItem item = cartItems.get(i);
                cartLine = String.format("%-20s %2dx %8s = %8s", 
                    item.getProductName().length() > 20 ? item.getProductName().substring(0, 17) + "..." : item.getProductName(),
                    item.getQuantity(),
                    currency.format(item.getUnitPrice()),
                    currency.format(item.getLineTotal()));
            } else {
                cartLine = " ".repeat(39);
            }
            
            System.out.println(productLine + " " + cartLine);
        }
    }
    
    private static void showDiscountSection() {
        System.out.println("\n[MANUAL DISCOUNT CONTROLS]");
        System.out.println("-".repeat(50));
        System.out.printf("Current Discount: %s\n", currency.format(currentTransaction.getDiscountAmount()));
        if (selectedCustomer != null) {
            System.out.printf("Suggested for %s member: %.0f%%\n", 
                selectedCustomer.getMembershipLevel(), selectedCustomer.getSuggestedDiscountPercentage() * 100);
        }
    }
    
    private static void showTotalsSection() {
        currentTransaction.recalculateAmounts();
        System.out.println("\n[TRANSACTION TOTALS]");
        System.out.println("-".repeat(50));
        System.out.printf("Subtotal: %15s\n", currency.format(currentTransaction.getSubtotal()));
        System.out.printf("Discount: %15s\n", currency.format(currentTransaction.getDiscountAmount()));
        System.out.printf("TOTAL:    %15s\n", currency.format(currentTransaction.getTotalAmount()));
    }
    
    private static void showMenu() {
        System.out.println("\n[MENU OPTIONS]");
        System.out.println("-".repeat(50));
        System.out.println("1. Search/Select Customer");
        System.out.println("2. Add Product to Cart");
        System.out.println("3. Remove Item from Cart");
        System.out.println("4. Apply Manual Discount");
        System.out.println("5. Clear Discount");
        System.out.println("6. Process Payment");
        System.out.println("7. Start New Transaction");
        System.out.println("0. Exit");
        System.out.print("\nSelect option: ");
    }
    
    private static void searchCustomer() throws SQLException {
        System.out.print("Enter customer name, email, or phone: ");
        String searchTerm = scanner.nextLine().trim();
        
        if (searchTerm.isEmpty()) {
            selectedCustomer = null;
            currentTransaction.setCustomer(null);
            System.out.println("Customer cleared.");
        } else {
            List<Customer> customers = customerDAO.searchCustomers(searchTerm);
            if (customers.isEmpty()) {
                System.out.println("No customers found.");
            } else if (customers.size() == 1) {
                selectedCustomer = customers.get(0);
                currentTransaction.setCustomer(selectedCustomer);
                System.out.println("Customer selected: " + selectedCustomer.getFullName());
            } else {
                System.out.println("Multiple customers found:");
                for (int i = 0; i < customers.size(); i++) {
                    Customer c = customers.get(i);
                    System.out.printf("%d. %s (%s)\n", i+1, c.getFullName(), c.getEmail());
                }
                System.out.print("Select customer (1-" + customers.size() + "): ");
                try {
                    int choice = Integer.parseInt(scanner.nextLine().trim());
                    if (choice >= 1 && choice <= customers.size()) {
                        selectedCustomer = customers.get(choice - 1);
                        currentTransaction.setCustomer(selectedCustomer);
                        System.out.println("Customer selected: " + selectedCustomer.getFullName());
                    } else {
                        System.out.println("Invalid selection.");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("Invalid input.");
                }
            }
        }
        
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
    
    private static void addProductToCart() throws SQLException {
        System.out.print("Enter product SKU: ");
        String sku = scanner.nextLine().trim().toUpperCase();
        
        if (sku.isEmpty()) {
            System.out.println("SKU cannot be empty.");
            System.out.println("Press Enter to continue...");
            scanner.nextLine();
            return;
        }
        
        var productOpt = productDAO.findBySku(sku);
        if (productOpt.isEmpty()) {
            System.out.println("Product not found.");
            System.out.println("Press Enter to continue...");
            scanner.nextLine();
            return;
        }
        
        Product product = productOpt.get();
        System.out.printf("Product: %s - %s (Stock: %d)\n", 
            product.getName(), currency.format(product.getPrice()), product.getStockQuantity());
        
        System.out.print("Enter quantity: ");
        try {
            int quantity = Integer.parseInt(scanner.nextLine().trim());
            if (quantity <= 0) {
                System.out.println("Quantity must be positive.");
            } else if (quantity > product.getStockQuantity()) {
                System.out.println("Insufficient stock.");
            } else {
                TransactionItem item = new TransactionItem(product, quantity);
                currentTransaction.addItem(item);
                System.out.println("Item added to cart.");
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid quantity.");
        }
        
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
    
    private static void removeFromCart() {
        if (currentTransaction.getItems().isEmpty()) {
            System.out.println("Cart is empty.");
            System.out.println("Press Enter to continue...");
            scanner.nextLine();
            return;
        }
        
        System.out.println("Cart items:");
        List<TransactionItem> items = currentTransaction.getItems();
        for (int i = 0; i < items.size(); i++) {
            TransactionItem item = items.get(i);
            System.out.printf("%d. %s (Qty: %d)\n", i+1, item.getProductName(), item.getQuantity());
        }
        
        System.out.print("Select item to remove (1-" + items.size() + "): ");
        try {
            int choice = Integer.parseInt(scanner.nextLine().trim());
            if (choice >= 1 && choice <= items.size()) {
                items.remove(choice - 1);
                System.out.println("Item removed from cart.");
            } else {
                System.out.println("Invalid selection.");
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid input.");
        }
        
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
    
    private static void applyManualDiscount() {
        System.out.println("Enter discount amount ($) or percentage (%):");
        System.out.print("Example: '10.50' for $10.50 or '15%' for 15%: ");
        String input = scanner.nextLine().trim();
        
        if (input.isEmpty()) {
            System.out.println("No discount entered.");
        } else {
            try {
                if (input.endsWith("%")) {
                    double percentage = Double.parseDouble(input.substring(0, input.length() - 1));
                    if (percentage >= 0 && percentage <= 100) {
                        currentTransaction.applyManualDiscountPercentage(percentage / 100.0);
                        System.out.printf("Applied %.1f%% discount.\n", percentage);
                    } else {
                        System.out.println("Percentage must be between 0 and 100.");
                    }
                } else {
                    double amount = Double.parseDouble(input);
                    if (amount >= 0) {
                        currentTransaction.applyManualDiscount(new BigDecimal(amount));
                        System.out.printf("Applied %s discount.\n", currency.format(amount));
                    } else {
                        System.out.println("Discount amount cannot be negative.");
                    }
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid input format.");
            }
        }
        
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
    
    private static void clearDiscount() {
        currentTransaction.clearDiscount();
        System.out.println("Discount cleared.");
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
    
    private static void processPayment() {
        if (currentTransaction.getItems().isEmpty()) {
            System.out.println("Cart is empty. Cannot process payment.");
            System.out.println("Press Enter to continue...");
            scanner.nextLine();
            return;
        }
        
        currentTransaction.recalculateAmounts();
        System.out.println("PAYMENT SUMMARY:");
        System.out.println("Total Amount: " + currency.format(currentTransaction.getTotalAmount()));
        System.out.print("Confirm payment? (y/n): ");
        
        String confirm = scanner.nextLine().trim().toLowerCase();
        if ("y".equals(confirm) || "yes".equals(confirm)) {
            try {
                currentTransaction.setPaymentMethod("CASH");
                Transaction completed = transactionService.processTransaction(currentTransaction);
                
                System.out.println("\nPAYMENT SUCCESSFUL!");
                System.out.println("Transaction Number: " + completed.getTransactionNumber());
                System.out.println("Receipt printed!");
                
                if (selectedCustomer != null) {
                    Customer updated = customerDAO.findById(selectedCustomer.getId()).get();
                    int pointsEarned = updated.getLoyaltyPoints() - selectedCustomer.getLoyaltyPoints();
                    System.out.println("Customer earned " + pointsEarned + " loyalty points!");
                }
                
                startNewTransaction();
                
            } catch (Exception e) {
                System.out.println("Payment failed: " + e.getMessage());
            }
        } else {
            System.out.println("Payment cancelled.");
        }
        
        System.out.println("Press Enter to continue...");
        scanner.nextLine();
    }
}

@echo off
title Clothing Store POS System - Build Script
echo ========================================
echo  CLOTHING STORE POS SYSTEM - BUILD
echo ========================================
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

echo Creating directories...
if not exist "bin" mkdir bin
if not exist "lib" mkdir lib

echo.
echo Checking dependencies...
if not exist "lib\sqlite-jdbc-********.jar" (
    echo Downloading SQLite JDBC driver...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar' -OutFile 'lib/sqlite-jdbc-********.jar'"
    if errorlevel 1 (
        echo ERROR: Failed to download SQLite JDBC driver
        pause
        exit /b 1
    )
    echo SQLite JDBC driver downloaded successfully!
) else (
    echo SQLite JDBC driver already exists.
)

echo.
echo Compiling core classes...
javac -cp "lib/sqlite-jdbc-********.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java

if errorlevel 1 (
    echo ERROR: Core compilation failed
    pause
    exit /b 1
)

echo Compiling utilities...
javac -cp "bin;lib/sqlite-jdbc-********.jar" -d bin src/com/clothingstore/util/ValidationUtil.java src/com/clothingstore/util/FormatUtil.java

if errorlevel 1 (
    echo ERROR: Utilities compilation failed
    pause
    exit /b 1
)

echo Compiling GUI components...
javac -cp "bin;lib/sqlite-jdbc-********.jar" -d bin src/com/clothingstore/gui/*.java

if errorlevel 1 (
    echo ERROR: GUI compilation failed
    pause
    exit /b 1
)

echo Compiling demos and tests...
javac -cp "bin;lib/sqlite-jdbc-********.jar" -d bin src/com/clothingstore/demo/*.java src/com/clothingstore/test/*.java

if errorlevel 1 (
    echo ERROR: Demo/Test compilation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo Available shortcuts:
echo - Launch-POS-GUI.bat       : Start the main POS application
echo - Launch-Console-Demo.bat  : Run console demonstration
echo - Manual-Discount-Demo.bat : Show manual discount features
echo - Run-Tests.bat           : Execute test suite
echo.
echo Project is ready to use!
pause

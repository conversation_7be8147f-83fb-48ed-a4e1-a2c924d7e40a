package com.clothingstore.test;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.TransactionService;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Comprehensive test for Transaction Service and POS functionality
 */
public class TransactionServiceTest {
    
    public static void main(String[] args) {
        System.out.println("=== Transaction Service & POS Test ===");
        
        try {
            // Initialize database
            System.out.println("Initializing database...");
            DatabaseManager.getInstance().initializeDatabase();
            System.out.println("SUCCESS: Database initialized successfully");
            
            // Test complete POS workflow
            testCompleteTransactionWorkflow();
            
            // Test inventory management
            testInventoryManagement();
            
            // Test customer loyalty program
            testCustomerLoyaltyProgram();
            
            // Test transaction reporting
            testTransactionReporting();
            
            System.out.println("\n=== All POS tests completed successfully! ===");
            
        } catch (Exception e) {
            System.err.println("ERROR: Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up
            DatabaseManager.getInstance().closeConnection();
        }
    }
    
    private static void testCompleteTransactionWorkflow() throws SQLException, TransactionService.InsufficientStockException {
        System.out.println("\n--- Testing Complete Transaction Workflow ---");
        
        ProductDAO productDAO = ProductDAO.getInstance();
        CustomerDAO customerDAO = CustomerDAO.getInstance();
        TransactionService transactionService = TransactionService.getInstance();
        
        // Get some products for the transaction
        List<Product> products = productDAO.findAll();
        Product tshirt = products.stream()
            .filter(p -> p.getName().contains("T-Shirt"))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("T-Shirt not found"));
        
        Product jeans = products.stream()
            .filter(p -> p.getName().contains("Jeans"))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("Jeans not found"));
        
        // Get a customer
        List<Customer> customers = customerDAO.findAll();
        Customer customer = customers.get(0);
        
        System.out.println("Creating transaction for customer: " + customer.getFullName());
        System.out.println("Initial customer points: " + customer.getLoyaltyPoints());
        System.out.println("Initial customer total spent: $" + customer.getTotalSpent());
        
        // Record initial stock levels
        int initialTshirtStock = tshirt.getStockQuantity();
        int initialJeansStock = jeans.getStockQuantity();
        
        // Create a new transaction
        Transaction transaction = new Transaction();
        transaction.setCustomer(customer);
        transaction.setCashierName("Test Cashier");
        transaction.setPaymentMethod("CREDIT_CARD");
        transaction.setNotes("Test transaction");
        
        // Add items to transaction
        TransactionItem item1 = new TransactionItem(tshirt, 2);
        TransactionItem item2 = new TransactionItem(jeans, 1);
        
        transaction.addItem(item1);
        transaction.addItem(item2);
        
        System.out.println("Transaction items:");
        System.out.println("  - " + tshirt.getName() + " x2 @ $" + tshirt.getPrice() + " each");
        System.out.println("  - " + jeans.getName() + " x1 @ $" + jeans.getPrice());
        
        System.out.println("Subtotal: $" + transaction.getSubtotal());
        System.out.println("Customer discount: $" + transaction.getDiscountAmount());
        System.out.println("Tax: $" + transaction.getTaxAmount());
        System.out.println("Total: $" + transaction.getTotalAmount());
        
        // Process the transaction
        Transaction completedTransaction = transactionService.processTransaction(transaction);
        System.out.println("SUCCESS: Transaction processed with ID: " + completedTransaction.getId());
        System.out.println("Transaction number: " + completedTransaction.getTransactionNumber());
        
        // Verify stock was reduced
        Product updatedTshirt = productDAO.findById(tshirt.getId()).get();
        Product updatedJeans = productDAO.findById(jeans.getId()).get();
        
        System.out.println("Stock updates:");
        System.out.println("  - T-Shirt: " + initialTshirtStock + " -> " + updatedTshirt.getStockQuantity());
        System.out.println("  - Jeans: " + initialJeansStock + " -> " + updatedJeans.getStockQuantity());
        
        // Verify customer loyalty points were updated
        Customer updatedCustomer = customerDAO.findById(customer.getId()).get();
        System.out.println("Customer updates:");
        System.out.println("  - Points: " + customer.getLoyaltyPoints() + " -> " + updatedCustomer.getLoyaltyPoints());
        System.out.println("  - Total spent: $" + customer.getTotalSpent() + " -> $" + updatedCustomer.getTotalSpent());
        System.out.println("  - Membership: " + updatedCustomer.getMembershipLevel());
        
        System.out.println("SUCCESS: Complete transaction workflow tested");
    }
    
    private static void testInventoryManagement() throws SQLException {
        System.out.println("\n--- Testing Inventory Management ---");
        
        ProductDAO productDAO = ProductDAO.getInstance();
        
        // Test low stock detection
        List<Product> lowStockProducts = productDAO.findLowStockProducts();
        System.out.println("Low stock products: " + lowStockProducts.size());
        
        // Test stock adjustment
        List<Product> products = productDAO.findAll();
        Product testProduct = products.get(0);
        int originalStock = testProduct.getStockQuantity();
        
        System.out.println("Testing stock adjustment for: " + testProduct.getName());
        System.out.println("Original stock: " + originalStock);
        
        // Reduce stock
        productDAO.updateStock(testProduct.getId(), originalStock - 5);
        Product updatedProduct = productDAO.findById(testProduct.getId()).get();
        System.out.println("After reduction: " + updatedProduct.getStockQuantity());
        
        // Restore stock
        productDAO.updateStock(testProduct.getId(), originalStock);
        updatedProduct = productDAO.findById(testProduct.getId()).get();
        System.out.println("After restoration: " + updatedProduct.getStockQuantity());
        
        System.out.println("SUCCESS: Inventory management tested");
    }
    
    private static void testCustomerLoyaltyProgram() throws SQLException {
        System.out.println("\n--- Testing Customer Loyalty Program ---");
        
        CustomerDAO customerDAO = CustomerDAO.getInstance();
        
        // Test membership levels
        List<Customer> bronzeCustomers = customerDAO.findByMembershipLevel("BRONZE");
        List<Customer> silverCustomers = customerDAO.findByMembershipLevel("SILVER");
        List<Customer> goldCustomers = customerDAO.findByMembershipLevel("GOLD");
        List<Customer> platinumCustomers = customerDAO.findByMembershipLevel("PLATINUM");
        
        System.out.println("Membership distribution:");
        System.out.println("  - Bronze: " + bronzeCustomers.size());
        System.out.println("  - Silver: " + silverCustomers.size());
        System.out.println("  - Gold: " + goldCustomers.size());
        System.out.println("  - Platinum: " + platinumCustomers.size());
        
        // Test top customers
        List<Customer> topCustomers = customerDAO.findTopCustomers(5);
        System.out.println("Top customers by spending:");
        for (int i = 0; i < topCustomers.size(); i++) {
            Customer c = topCustomers.get(i);
            System.out.println("  " + (i+1) + ". " + c.getFullName() + " - $" + c.getTotalSpent() + 
                             " (" + c.getMembershipLevel() + ", " + c.getLoyaltyPoints() + " points)");
        }
        
        // Test loyalty point redemption
        Customer testCustomer = topCustomers.get(0);
        int originalPoints = testCustomer.getLoyaltyPoints();
        System.out.println("Testing point redemption for: " + testCustomer.getFullName());
        System.out.println("Original points: " + originalPoints);
        
        if (testCustomer.redeemLoyaltyPoints(50)) {
            customerDAO.updateLoyaltyPoints(testCustomer.getId(), testCustomer.getLoyaltyPoints());
            System.out.println("After redeeming 50 points: " + testCustomer.getLoyaltyPoints());
            
            // Restore points
            testCustomer.addLoyaltyPoints(50);
            customerDAO.updateLoyaltyPoints(testCustomer.getId(), testCustomer.getLoyaltyPoints());
            System.out.println("Points restored: " + testCustomer.getLoyaltyPoints());
        } else {
            System.out.println("Insufficient points for redemption");
        }
        
        System.out.println("SUCCESS: Customer loyalty program tested");
    }
    
    private static void testTransactionReporting() throws SQLException {
        System.out.println("\n--- Testing Transaction Reporting ---");
        
        TransactionDAO transactionDAO = TransactionDAO.getInstance();
        TransactionService transactionService = TransactionService.getInstance();
        
        // Get all transactions
        List<Transaction> allTransactions = transactionDAO.findAll();
        System.out.println("Total transactions in system: " + allTransactions.size());
        
        // Test date range queries
        LocalDateTime today = LocalDateTime.now();
        LocalDateTime startOfDay = today.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = today.toLocalDate().atTime(23, 59, 59);
        
        List<Transaction> todayTransactions = transactionDAO.findByDateRange(startOfDay, endOfDay);
        System.out.println("Transactions today: " + todayTransactions.size());
        
        // Test sales summary
        TransactionService.SalesSummary summary = transactionService.getDailySales(today);
        System.out.println("Daily sales summary:");
        System.out.println("  - Transaction count: " + summary.getTransactionCount());
        System.out.println("  - Item count: " + summary.getItemCount());
        System.out.println("  - Total sales: $" + summary.getTotalSales());
        System.out.println("  - Total tax: $" + summary.getTotalTax());
        System.out.println("  - Total discount: $" + summary.getTotalDiscount());
        System.out.println("  - Average transaction: $" + summary.getAverageTransactionValue());
        
        // Test customer transaction history
        if (!allTransactions.isEmpty()) {
            Transaction sampleTransaction = allTransactions.get(0);
            if (sampleTransaction.getCustomerId() != null) {
                List<Transaction> customerHistory = transactionService.getCustomerTransactionHistory(
                    sampleTransaction.getCustomerId());
                System.out.println("Customer transaction history: " + customerHistory.size() + " transactions");
            }
        }
        
        System.out.println("SUCCESS: Transaction reporting tested");
    }
}

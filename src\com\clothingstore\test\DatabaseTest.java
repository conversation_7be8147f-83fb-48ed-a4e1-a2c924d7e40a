package com.clothingstore.test;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;

/**
 * Simple test class to verify database functionality without JavaFX
 */
public class DatabaseTest {
    
    public static void main(String[] args) {
        System.out.println("=== Clothing Store Database Test ===");
        
        try {
            // Initialize database
            System.out.println("Initializing database...");
            DatabaseManager.getInstance().initializeDatabase();
            System.out.println("SUCCESS: Database initialized successfully");
            
            // Test Product operations
            testProductOperations();
            
            // Test Customer operations
            testCustomerOperations();
            
            System.out.println("\n=== All tests completed successfully! ===");
            
        } catch (Exception e) {
            System.err.println("ERROR: Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up
            DatabaseManager.getInstance().closeConnection();
        }
    }
    
    private static void testProductOperations() throws SQLException {
        System.out.println("\n--- Testing Product Operations ---");
        
        ProductDAO productDAO = ProductDAO.getInstance();
        
        // Test finding all products
        List<Product> products = productDAO.findAll();
        System.out.println("SUCCESS: Found " + products.size() + " products");

        // Display first few products
        for (int i = 0; i < Math.min(3, products.size()); i++) {
            Product p = products.get(i);
            System.out.println("  - " + p.getName() + " (" + p.getSku() + ") - $" + p.getPrice() + " - Stock: " + p.getStockQuantity());
        }

        // Test search functionality
        List<Product> searchResults = productDAO.searchProducts("shirt");
        System.out.println("SUCCESS: Search for 'shirt' found " + searchResults.size() + " products");

        // Test low stock products
        List<Product> lowStockProducts = productDAO.findLowStockProducts();
        System.out.println("SUCCESS: Found " + lowStockProducts.size() + " low stock products");

        // Test categories
        List<String> categories = productDAO.getAllCategories();
        System.out.println("SUCCESS: Found " + categories.size() + " categories: " + categories);
        
        // Test adding a new product
        Product newProduct = new Product();
        newProduct.setSku("TEST001");
        newProduct.setName("Test Product");
        newProduct.setCategory("Test");
        newProduct.setSize("M");
        newProduct.setColor("Blue");
        newProduct.setPrice(new BigDecimal("29.99"));
        newProduct.setCostPrice(new BigDecimal("15.00"));
        newProduct.setStockQuantity(10);
        newProduct.setMinStockLevel(5);
        
        Product savedProduct = productDAO.save(newProduct);
        System.out.println("SUCCESS: Added new product with ID: " + savedProduct.getId());

        // Test updating stock
        productDAO.updateStock(savedProduct.getId(), 15);
        System.out.println("SUCCESS: Updated stock for test product");

        // Clean up - delete test product
        productDAO.delete(savedProduct.getId());
        System.out.println("SUCCESS: Deleted test product");
    }
    
    private static void testCustomerOperations() throws SQLException {
        System.out.println("\n--- Testing Customer Operations ---");
        
        CustomerDAO customerDAO = CustomerDAO.getInstance();
        
        // Test finding all customers
        List<Customer> customers = customerDAO.findAll();
        System.out.println("SUCCESS: Found " + customers.size() + " customers");

        // Display first few customers
        for (int i = 0; i < Math.min(3, customers.size()); i++) {
            Customer c = customers.get(i);
            System.out.println("  - " + c.getFullName() + " (" + c.getEmail() + ") - " + c.getMembershipLevel() + " - Points: " + c.getLoyaltyPoints());
        }

        // Test search functionality
        List<Customer> searchResults = customerDAO.searchCustomers("john");
        System.out.println("SUCCESS: Search for 'john' found " + searchResults.size() + " customers");

        // Test membership levels
        List<Customer> goldCustomers = customerDAO.findByMembershipLevel("GOLD");
        System.out.println("SUCCESS: Found " + goldCustomers.size() + " GOLD customers");

        // Test top customers
        List<Customer> topCustomers = customerDAO.findTopCustomers(5);
        System.out.println("SUCCESS: Found top " + topCustomers.size() + " customers by spending");
        
        // Test adding a new customer
        Customer newCustomer = new Customer();
        newCustomer.setFirstName("Test");
        newCustomer.setLastName("Customer");
        newCustomer.setEmail("<EMAIL>");
        newCustomer.setPhone("555-TEST");
        newCustomer.setAddress("123 Test St");
        newCustomer.setCity("Test City");
        newCustomer.setState("TS");
        newCustomer.setZipCode("12345");
        
        Customer savedCustomer = customerDAO.save(newCustomer);
        System.out.println("SUCCESS: Added new customer with ID: " + savedCustomer.getId());

        // Test updating purchase history
        customerDAO.updatePurchaseHistory(savedCustomer.getId(), 100.0);
        System.out.println("SUCCESS: Updated purchase history for test customer");

        // Clean up - delete test customer
        customerDAO.delete(savedCustomer.getId());
        System.out.println("SUCCESS: Deleted test customer");
    }
}

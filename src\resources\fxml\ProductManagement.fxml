<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ProductManagementController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Product Management">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnAddProduct" onAction="#handleAddProduct" styleClass="button success" text="+ Add Product" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Search and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-label" text="Search:" />
            <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" prefWidth="200.0" promptText="Search products..." />
            <Label styleClass="form-label" text="Category:" />
            <ComboBox fx:id="cmbCategory" onAction="#handleCategoryFilter" prefWidth="150.0" promptText="All Categories" />
            <Label styleClass="form-label" text="Stock Status:" />
            <ComboBox fx:id="cmbStockStatus" onAction="#handleStockFilter" prefWidth="120.0" promptText="All" />
            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" styleClass="button secondary" text="Clear Filters" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Products Table -->
      <TableView fx:id="tblProducts" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colSku" prefWidth="100.0" text="SKU" />
            <TableColumn fx:id="colName" prefWidth="200.0" text="Product Name" />
            <TableColumn fx:id="colCategory" prefWidth="120.0" text="Category" />
            <TableColumn fx:id="colBrand" prefWidth="100.0" text="Brand" />
            <TableColumn fx:id="colSize" prefWidth="60.0" text="Size" />
            <TableColumn fx:id="colColor" prefWidth="80.0" text="Color" />
            <TableColumn fx:id="colPrice" prefWidth="80.0" text="Price" />
            <TableColumn fx:id="colStock" prefWidth="80.0" text="Stock" />
            <TableColumn fx:id="colMinStock" prefWidth="80.0" text="Min Stock" />
            <TableColumn fx:id="colStatus" prefWidth="80.0" text="Status" />
            <TableColumn fx:id="colActions" prefWidth="120.0" text="Actions" />
         </columns>
         <contextMenu>
            <ContextMenu>
               <items>
                  <MenuItem fx:id="menuEdit" onAction="#handleEditProduct" text="Edit Product" />
                  <MenuItem fx:id="menuDuplicate" onAction="#handleDuplicateProduct" text="Duplicate Product" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuAdjustStock" onAction="#handleAdjustStock" text="Adjust Stock" />
                  <MenuItem fx:id="menuViewHistory" onAction="#handleViewHistory" text="View History" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuDelete" onAction="#handleDeleteProduct" text="Delete Product" />
               </items>
            </ContextMenu>
         </contextMenu>
      </TableView>

      <!-- Summary Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="form-container">
         <children>
            <Label fx:id="lblTotalProducts" styleClass="form-label" text="Total Products: 0" />
            <Label fx:id="lblLowStockCount" styleClass="form-label" text="Low Stock Items: 0" />
            <Label fx:id="lblTotalValue" styleClass="form-label" text="Total Inventory Value: $0.00" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
            <Button fx:id="btnLowStockReport" onAction="#handleLowStockReport" styleClass="button warning" text="⚠ Low Stock Report" />
         </children>
         <VBox.margin>
            <Insets top="10.0" />
         </VBox.margin>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>

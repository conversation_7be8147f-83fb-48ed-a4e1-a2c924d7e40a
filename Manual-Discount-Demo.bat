@echo off
title Clothing Store POS System - Manual Discount Demo
echo ========================================
echo  MANUAL DISCOUNT SYSTEM DEMONSTRATION
echo ========================================
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "bin\com\clothingstore\demo\ManualDiscountDemo.class" (
    echo Compiling application...
    javac -cp "lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java src/com/clothingstore/demo/ManualDiscountDemo.java
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo Compilation successful!
    echo.
)

echo Running Manual Discount System Demo...
echo.
echo This demo shows:
echo - No automatic discounts
echo - Manual discount application
echo - Percentage and dollar amount discounts
echo - Discount clearing functionality
echo.
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.demo.ManualDiscountDemo

echo.
echo Manual discount demo completed.
pause

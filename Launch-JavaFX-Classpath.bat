@echo off
title Clothing Store POS System - JavaFX with Classpath
echo ========================================
echo  CLOTHING STORE POS SYSTEM - JavaFX
echo ========================================
echo.
echo Starting JavaFX GUI with classpath approach...
echo.

cd /d "%~dp0"

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM Check if JavaFX SDK exists
if not exist "javafx-sdk-11.0.2\lib" (
    echo ERROR: JavaFX SDK not found
    echo Please ensure JavaFX SDK is extracted in javafx-sdk-11.0.2 folder
    pause
    exit /b 1
)

REM Build JavaFX classpath
set JAVAFX_CP=javafx-sdk-11.0.2\lib\javafx.base.jar;javafx-sdk-11.0.2\lib\javafx.controls.jar;javafx-sdk-11.0.2\lib\javafx.fxml.jar;javafx-sdk-11.0.2\lib\javafx.graphics.jar;javafx-sdk-11.0.2\lib\javafx.media.jar;javafx-sdk-11.0.2\lib\javafx.swing.jar;javafx-sdk-11.0.2\lib\javafx.web.jar

REM Compile if needed
if not exist "bin\com\clothingstore\BasicJavaFXApp.class" (
    echo Compiling JavaFX application with classpath approach...
    javac -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar;%JAVAFX_CP%" -d bin src/com/clothingstore/BasicJavaFXApp.java
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo Compilation successful!
    echo.
)

echo Launching JavaFX Clothing Store POS System...
echo Using classpath approach for maximum compatibility...
echo.
echo Features:
echo - JavaFX GUI with direct classpath loading
echo - No module system dependencies
echo - Maximum compatibility across Java versions
echo - Professional interface with inline styling
echo - Direct access to all POS system components
echo.
echo ========================================
echo.

REM Launch JavaFX application with classpath approach
echo Starting JavaFX application...
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar;%JAVAFX_CP%" com.clothingstore.BasicJavaFXApp

if errorlevel 1 (
    echo.
    echo JavaFX classpath launch failed. Trying module approach...
    echo.
    
    REM Fallback: Try module approach
    java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" --module-path "javafx-sdk-11.0.2/lib" --add-modules javafx.controls,javafx.fxml com.clothingstore.BasicJavaFXApp
    
    if errorlevel 1 (
        echo.
        echo Both JavaFX approaches failed. Launching Swing POS as fallback...
        java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.gui.SwingPOSDemo
        
        if errorlevel 1 (
            echo.
            echo All GUI attempts failed. Running console demo...
            java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.demo.SimplePOSDemo
        )
    )
)

echo.
echo Application session ended.
pause
